{"name": "wechat-notifier", "version": "0.1.0", "private": true, "scripts": {"predev": "node scripts/init-db.js", "dev": "next dev", "prebuild": "node scripts/init-db.js", "build": "next build", "prestart": "node scripts/init-db.js", "start": "next start", "start:custom": "node server.js", "start:standalone": "node start.js", "lint": "next lint", "db:init": "node scripts/init-db.js", "db:init-json": "node scripts/init-jsondb.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "node scripts/migrate-from-json.js", "db:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^5.22.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/xml2js": "^0.4.14", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "next": "14.2.3", "react": "^18", "react-dom": "^18", "xml2js": "^0.6.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "prisma": "^5.22.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}