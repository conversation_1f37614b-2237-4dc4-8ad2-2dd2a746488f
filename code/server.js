// 自定义服务器入口文件
const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');

// 检查并初始化数据库
function checkDatabase() {
  console.log('正在检查数据库状态...');

  try {
    // 检查脚本是否存在
    const scriptPath = path.join(__dirname, 'scripts/init-jsondb.js');
    if (fs.existsSync(scriptPath)) {
      console.log('执行数据库初始化脚本...');
      execSync(`node ${scriptPath}`, { stdio: 'inherit' });
    } else {
      console.warn('数据库初始化脚本不存在:', scriptPath);
      console.log('跳过数据库初始化');
    }
  } catch (error) {
    console.error('数据库初始化失败:', error.message);
    console.log('继续启动应用程序...');
  }
}

// 启动 Next.js 服务器
function startServer() {
  // 检查是否为 standalone 模式
  const isStandalone = fs.existsSync(path.join(__dirname, '.next/standalone'));

  if (isStandalone) {
    console.log('以 standalone 模式启动服务器...');
    // 加载 standalone 服务器
    require('./.next/standalone/server.js');
  } else {
    console.log('以标准模式启动服务器...');
    // 加载 Next.js
    const next = require('next');
    const http = require('http');

    const dev = process.env.NODE_ENV !== 'production';
    const hostname = process.env.HOSTNAME || 'localhost';
    const port = parseInt(process.env.PORT || '3000', 10);

    const app = next({ dev, hostname, port });
    const handle = app.getRequestHandler();

    app.prepare().then(() => {
      const server = http.createServer((req, res) => {
        handle(req, res);
      });

      server.listen(port, (err) => {
        if (err) throw err;
        console.log(`> Ready on http://${hostname}:${port}`);
      });
    });
  }
}

// 主函数
function main() {
  // 检查并初始化数据库
  checkDatabase();

  // 启动服务器
  startServer();
}

// 执行主函数
main();
