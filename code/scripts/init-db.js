const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

async function initDatabase() {
  try {
    console.log('开始初始化Prisma数据库...');

    // 检查.env文件是否存在
    const envPath = path.join(process.cwd(), '.env');
    if (!fs.existsSync(envPath)) {
      console.error('.env文件不存在，请先创建.env文件');
      process.exit(1);
    }

    // 读取DATABASE_URL
    const envContent = fs.readFileSync(envPath, 'utf8');
    const databaseUrlMatch = envContent.match(/DATABASE_URL="(.+)"/);
    if (!databaseUrlMatch) {
      console.error('.env文件中未找到DATABASE_URL配置');
      process.exit(1);
    }

    const databaseUrl = databaseUrlMatch[1];
    console.log(`数据库URL: ${databaseUrl}`);

    // 如果是SQLite文件，确保目录存在
    if (databaseUrl.startsWith('file:')) {
      const dbPath = databaseUrl.replace('file:', '');
      const dbDir = path.dirname(dbPath);
      if (!fs.existsSync(dbDir)) {
        console.log(`创建数据库目录: ${dbDir}`);
        fs.mkdirSync(dbDir, { recursive: true });
      }
    }

    // 生成Prisma客户端
    console.log('生成Prisma客户端...');
    execSync('npx prisma generate', { stdio: 'inherit' });

    // 推送数据库schema
    console.log('推送数据库schema...');
    execSync('npx prisma db push', { stdio: 'inherit' });

    console.log('数据库初始化完成！');

    // 检查是否需要从JSON迁移数据
    const dbDir = './db';
    if (fs.existsSync(dbDir)) {
      const files = fs.readdirSync(dbDir);
      const hasData = files.some(file => {
        if (!file.endsWith('.json')) return false;
        const content = fs.readFileSync(path.join(dbDir, file), 'utf8');
        const data = JSON.parse(content || '[]');
        return Array.isArray(data) && data.length > 0;
      });

      if (hasData) {
        console.log('\n检测到JSON数据库中有数据，是否要迁移到SQLite？');
        console.log('运行以下命令进行迁移：');
        console.log('npm run db:migrate');
      }
    }

  } catch (error) {
    console.error('数据库初始化失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase();
}

module.exports = { initDatabase };
