const fs = require('fs');
const path = require('path');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function migrateFromJson() {
  try {
    console.log('开始从JSON数据库迁移到SQLite...');

    // 读取JSON数据
    const dbDir = './db';
    
    const usersData = JSON.parse(fs.readFileSync(path.join(dbDir, 'users.json'), 'utf8') || '[]');
    const templatesData = JSON.parse(fs.readFileSync(path.join(dbDir, 'templates.json'), 'utf8') || '[]');
    const qrcodesData = JSON.parse(fs.readFileSync(path.join(dbDir, 'qrcodes.json'), 'utf8') || '[]');
    const subscribersData = JSON.parse(fs.readFileSync(path.join(dbDir, 'subscribers.json'), 'utf8') || '[]');
    const notificationLogsData = JSON.parse(fs.readFileSync(path.join(dbDir, 'notificationLogs.json'), 'utf8') || '[]');

    console.log(`找到数据: ${usersData.length} 用户, ${templatesData.length} 模板, ${qrcodesData.length} 二维码, ${subscribersData.length} 订阅者, ${notificationLogsData.length} 通知日志`);

    // 清空现有数据
    console.log('清空现有数据...');
    await prisma.notificationLog.deleteMany();
    await prisma.subscriber.deleteMany();
    await prisma.qRCode.deleteMany();
    await prisma.template.deleteMany();
    await prisma.user.deleteMany();

    // 迁移用户数据
    console.log('迁移用户数据...');
    for (const user of usersData) {
      await prisma.user.create({
        data: {
          id: user.id,
          username: user.username,
          password: user.password
        }
      });
    }

    // 迁移模板数据
    console.log('迁移模板数据...');
    for (const template of templatesData) {
      await prisma.template.create({
        data: {
          id: template.id,
          templateId: template.templateId,
          name: template.name,
          alias: template.alias,
          description: template.description,
          variables: template.variables,
          isActive: template.isActive
        }
      });
    }

    // 迁移二维码数据
    console.log('迁移二维码数据...');
    for (const qrcode of qrcodesData) {
      await prisma.qRCode.create({
        data: {
          id: qrcode.id,
          name: qrcode.name,
          sceneStr: qrcode.sceneStr,
          templateId: qrcode.templateId,
          templateData: qrcode.templateData,
          firstNotifyDate: new Date(qrcode.firstNotifyDate),
          monthlyNotifyDay: qrcode.monthlyNotifyDay,
          isActive: qrcode.isActive,
          endDate: qrcode.endDate ? new Date(qrcode.endDate) : null,
          createdAt: new Date(qrcode.createdAt),
          updatedAt: new Date(qrcode.updatedAt),
          qrTicket: qrcode.qrTicket,
          qrUrl: qrcode.qrUrl,
          qrImgUrl: qrcode.qrImgUrl,
          qrGeneratedAt: qrcode.qrGeneratedAt ? new Date(qrcode.qrGeneratedAt) : null
        }
      });
    }

    // 迁移订阅者数据
    console.log('迁移订阅者数据...');
    for (const subscriber of subscribersData) {
      await prisma.subscriber.create({
        data: {
          id: subscriber.id,
          openid: subscriber.openid,
          qrcodeId: subscriber.qrcodeId,
          subscribedAt: new Date(subscriber.subscribedAt),
          isActive: subscriber.isActive
        }
      });
    }

    // 迁移通知日志数据
    console.log('迁移通知日志数据...');
    for (const log of notificationLogsData) {
      await prisma.notificationLog.create({
        data: {
          id: log.id,
          subscriberId: log.subscriberId,
          sentAt: new Date(log.sentAt),
          status: log.status,
          error: log.error
        }
      });
    }

    console.log('数据迁移完成！');
    
    // 验证迁移结果
    const counts = {
      users: await prisma.user.count(),
      templates: await prisma.template.count(),
      qrcodes: await prisma.qRCode.count(),
      subscribers: await prisma.subscriber.count(),
      logs: await prisma.notificationLog.count()
    };
    
    console.log('迁移结果验证:');
    console.log(`- 用户: ${counts.users}`);
    console.log(`- 模板: ${counts.templates}`);
    console.log(`- 二维码: ${counts.qrcodes}`);
    console.log(`- 订阅者: ${counts.subscribers}`);
    console.log(`- 通知日志: ${counts.logs}`);

  } catch (error) {
    console.error('迁移过程中发生错误:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  migrateFromJson()
    .then(() => {
      console.log('迁移成功完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('迁移失败:', error);
      process.exit(1);
    });
}

module.exports = { migrateFromJson };
