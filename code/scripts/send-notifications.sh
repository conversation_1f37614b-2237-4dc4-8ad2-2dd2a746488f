#!/bin/bash

# 微信通知发送脚本
# 每天凌晨4点执行，发送当天的通知消息

# 配置
API_URL="http://127.0.0.1:3000/api/scheduler"
SECRET_KEY="scheduler-secret-key"
LOG_FILE="/var/log/wechat-notifications.log"
MAX_LOG_SIZE=10485760  # 10MB

# 创建日志目录（如果不存在）
LOG_DIR=$(dirname "$LOG_FILE")
if [ ! -d "$LOG_DIR" ]; then
    sudo mkdir -p "$LOG_DIR"
    sudo chmod 755 "$LOG_DIR"
fi

# 日志轮转函数
rotate_log() {
    if [ -f "$LOG_FILE" ] && [ $(stat -f%z "$LOG_FILE" 2>/dev/null || stat -c%s "$LOG_FILE" 2>/dev/null) -gt $MAX_LOG_SIZE ]; then
        mv "$LOG_FILE" "${LOG_FILE}.old"
        touch "$LOG_FILE"
        chmod 644 "$LOG_FILE"
    fi
}

# 记录日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检查服务是否运行
check_service() {
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL" \
        -X POST \
        -H "x-scheduler-key: $SECRET_KEY" \
        -H "Content-Type: application/json" \
        --connect-timeout 10 \
        --max-time 30)
    
    if [ "$response" = "200" ] || [ "$response" = "401" ]; then
        return 0  # 服务运行中
    else
        return 1  # 服务未运行
    fi
}

# 发送通知函数
send_notifications() {
    log_message "开始发送每日通知..."
    
    # 调用API
    local response=$(curl -s -w "\n%{http_code}" "$API_URL" \
        -X POST \
        -H "x-scheduler-key: $SECRET_KEY" \
        -H "Content-Type: application/json" \
        --connect-timeout 10 \
        --max-time 120)
    
    # 分离响应体和状态码
    local http_code=$(echo "$response" | tail -n1)
    local response_body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" = "200" ]; then
        log_message "API调用成功: $response_body"
        
        # 解析响应（简单的文本解析）
        local sent=$(echo "$response_body" | grep -o '"sent":[0-9]*' | cut -d':' -f2)
        local failed=$(echo "$response_body" | grep -o '"failed":[0-9]*' | cut -d':' -f2)
        local total=$(echo "$response_body" | grep -o '"total":[0-9]*' | cut -d':' -f2)
        
        log_message "发送结果 - 总计: ${total:-0}, 成功: ${sent:-0}, 失败: ${failed:-0}"
        
        # 如果有失败的，记录详细信息
        if [ "${failed:-0}" -gt 0 ]; then
            log_message "发送失败详情: $response_body"
        fi
        
        return 0
    else
        log_message "API调用失败 - HTTP状态码: $http_code"
        log_message "响应内容: $response_body"
        return 1
    fi
}

# 主函数
main() {
    # 日志轮转
    rotate_log
    
    log_message "=== 开始执行微信通知发送任务 ==="
    
    # 检查服务状态
    if ! check_service; then
        log_message "错误: 微信通知服务未运行或无法访问 ($API_URL)"
        log_message "=== 任务执行失败 ==="
        exit 1
    fi
    
    log_message "服务状态检查通过"
    
    # 发送通知
    if send_notifications; then
        log_message "=== 任务执行成功 ==="
        exit 0
    else
        log_message "=== 任务执行失败 ==="
        exit 1
    fi
}

# 执行主函数
main "$@"
