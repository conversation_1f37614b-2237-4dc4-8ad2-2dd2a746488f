#!/bin/bash

# 微信通知系统 Cron 任务设置脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SCRIPT_PATH="$PROJECT_DIR/scripts/send-notifications.js"
LOG_DIR="$PROJECT_DIR/logs"
CRON_TIME="0 4 * * *"  # 每天凌晨4点

# 打印函数
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查权限
check_permissions() {
    if [ ! -w "$LOG_DIR" ]; then
        print_warn "没有日志目录写权限，可能需要 sudo 权限"
    fi
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."

    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi

    NODE_VERSION=$(node --version)
    print_info "Node.js 版本: $NODE_VERSION"

    # 检查 crontab
    if ! command -v crontab &> /dev/null; then
        print_error "crontab 未安装，请先安装 cron"
        exit 1
    fi

    print_info "依赖检查通过"
}

# 设置脚本权限
setup_script_permissions() {
    print_info "设置脚本权限..."

    if [ -f "$SCRIPT_PATH" ]; then
        chmod +x "$SCRIPT_PATH"
        print_info "脚本权限设置完成: $SCRIPT_PATH"
    else
        print_error "脚本文件不存在: $SCRIPT_PATH"
        exit 1
    fi
}

# 测试脚本
test_script() {
    print_info "测试通知脚本..."

    if node "$SCRIPT_PATH"; then
        print_info "脚本测试成功"
    else
        print_warn "脚本测试失败，请检查服务是否运行"
        print_warn "继续安装 cron 任务..."
    fi
}

# 安装 cron 任务
install_cron_job() {
    print_info "安装 cron 任务..."

    # 获取当前用户的 crontab
    TEMP_CRON=$(mktemp)
    crontab -l > "$TEMP_CRON" 2>/dev/null || true

    # 检查是否已存在相同的任务
    if grep -q "send-notifications.js" "$TEMP_CRON" 2>/dev/null; then
        print_warn "Cron 任务已存在，正在更新..."
        # 删除旧的任务
        grep -v "send-notifications.js" "$TEMP_CRON" > "${TEMP_CRON}.new" || true
        mv "${TEMP_CRON}.new" "$TEMP_CRON"
    fi

    # 添加新的任务
    echo "# 微信通知发送任务 - 每天凌晨4点执行" >> "$TEMP_CRON"
    echo "$CRON_TIME $(which node) $SCRIPT_PATH >> $LOG_DIR/wechat-cron.log 2>&1" >> "$TEMP_CRON"
    echo "" >> "$TEMP_CRON"

    # 安装新的 crontab
    crontab "$TEMP_CRON"
    rm "$TEMP_CRON"

    print_info "Cron 任务安装完成"
}

# 显示当前 cron 任务
show_cron_jobs() {
    print_info "当前的 cron 任务:"
    echo "----------------------------------------"
    crontab -l | grep -A1 -B1 "send-notifications" || print_warn "未找到相关的 cron 任务"
    echo "----------------------------------------"
}

# 创建日志目录
setup_log_directory() {
    print_info "设置日志目录..."

    # 尝试创建日志目录（如果需要）
    if [ ! -d "$LOG_DIR" ]; then
        if sudo mkdir -p "$LOG_DIR" 2>/dev/null; then
            print_info "日志目录创建成功: $LOG_DIR"
        else
            print_warn "无法创建日志目录，请手动创建: $LOG_DIR"
        fi
    fi

    # 设置日志文件权限
    LOG_FILE="$LOG_DIR/wechat-cron.log"
    if [ ! -f "$LOG_FILE" ]; then
        touch "$LOG_FILE" 2>/dev/null || true
    fi

    if [ -f "$LOG_FILE" ]; then
        chmod 644 "$LOG_FILE" 2>/dev/null || true
        print_info "日志文件设置完成: $LOG_FILE"
    fi
}

# 显示使用说明
show_usage() {
    print_info "Cron 任务设置完成！"
    echo ""
    echo "使用说明:"
    echo "1. 查看 cron 任务: crontab -l"
    echo "2. 编辑 cron 任务: crontab -e"
    echo "3. 删除 cron 任务: crontab -r"
    echo "4. 查看执行日志: tail -f $LOG_DIR/wechat-cron.log"
    echo "5. 手动测试脚本: node $SCRIPT_PATH"
    echo ""
    echo "时间配置说明:"
    echo "- 当前设置: $CRON_TIME (每天凌晨4点)"
    echo "- 修改时间: 编辑 crontab，格式为 '分 时 日 月 星期'"
    echo "- 例如 '0 6 * * *' 表示每天早上6点"
    echo "- 例如 '30 3 * * *' 表示每天凌晨3点30分"
    echo ""
    echo "重要提醒:"
    echo "- 确保微信通知服务在 http://127.0.0.1:3000 运行"
    echo "- 检查 .env 文件中的 SCHEDULER_SECRET_KEY 配置"
    echo "- 定期检查日志文件确保任务正常执行"
}

# 主函数
main() {
    echo "=== 微信通知系统 Cron 任务设置 ==="
    echo "项目目录: $PROJECT_DIR"
    echo "脚本路径: $SCRIPT_PATH"
    echo ""

    check_permissions
    check_dependencies
    setup_script_permissions
    setup_log_directory
    test_script
    install_cron_job
    show_cron_jobs
    show_usage

    print_info "设置完成！"
}

# 处理命令行参数
case "${1:-}" in
    "test")
        test_script
        ;;
    "install")
        main
        ;;
    "uninstall")
        print_info "卸载 cron 任务..."
        TEMP_CRON=$(mktemp)
        crontab -l > "$TEMP_CRON" 2>/dev/null || true
        grep -v "send-notifications.js" "$TEMP_CRON" > "${TEMP_CRON}.new" || true
        crontab "${TEMP_CRON}.new"
        rm "$TEMP_CRON" "${TEMP_CRON}.new"
        print_info "Cron 任务已卸载"
        ;;
    "status")
        show_cron_jobs
        ;;
    *)
        echo "用法: $0 {install|test|uninstall|status}"
        echo ""
        echo "命令说明:"
        echo "  install   - 安装 cron 任务"
        echo "  test      - 测试通知脚本"
        echo "  uninstall - 卸载 cron 任务"
        echo "  status    - 查看当前 cron 任务状态"
        echo ""
        echo "推荐使用: $0 install"
        exit 1
        ;;
esac
