#!/usr/bin/env node

/**
 * 微信通知发送脚本
 * 每天凌晨4点执行，发送当天的通知消息
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

// 配置
const CONFIG = {
  apiUrl: 'http://127.0.0.1:3000/api/scheduler',
  secretKey: 'scheduler-secret-key',
  logFile: path.join(__dirname, '..', 'logs', 'wechat-notifications.log'),
  maxLogSize: 10 * 1024 * 1024, // 10MB
  timeout: 30000, // 30秒超时
};

// 日志函数
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString().replace('T', ' ').slice(0, 19);
  const logMessage = `[${timestamp}] [${level}] ${message}`;

  console.log(logMessage);

  // 写入日志文件
  try {
    // 确保日志目录存在
    const logDir = path.dirname(CONFIG.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true, mode: 0o755 });
    }

    // 日志轮转
    if (fs.existsSync(CONFIG.logFile)) {
      const stats = fs.statSync(CONFIG.logFile);
      if (stats.size > CONFIG.maxLogSize) {
        fs.renameSync(CONFIG.logFile, `${CONFIG.logFile}.old`);
      }
    }

    fs.appendFileSync(CONFIG.logFile, logMessage + '\n');
  } catch (error) {
    console.error('写入日志文件失败:', error.message);
  }
}

// HTTP请求函数
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: CONFIG.timeout,
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data,
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

// 检查服务状态
async function checkService() {
  try {
    const response = await makeRequest(CONFIG.apiUrl, {
      method: 'POST',
      headers: {
        'x-scheduler-key': CONFIG.secretKey,
        'Content-Type': 'application/json',
      },
    });

    return response.statusCode === 200 || response.statusCode === 401;
  } catch (error) {
    log(`服务检查失败: ${error.message}`, 'ERROR');
    return false;
  }
}

// 发送通知
async function sendNotifications() {
  try {
    log('开始发送每日通知...');

    const response = await makeRequest(CONFIG.apiUrl, {
      method: 'POST',
      headers: {
        'x-scheduler-key': CONFIG.secretKey,
        'Content-Type': 'application/json',
      },
    });

    if (response.statusCode === 200) {
      const result = JSON.parse(response.body);
      log(`API调用成功: ${response.body}`);
      log(`发送结果 - 总计: ${result.total || 0}, 成功: ${result.sent || 0}, 失败: ${result.failed || 0}`);

      // 如果有失败的，记录详细信息
      if (result.failed > 0 && result.logs && result.logs.length > 0) {
        log('发送失败详情:', 'WARN');
        result.logs.forEach((logEntry, index) => {
          log(`  ${index + 1}. OpenID: ${logEntry.openid}, 错误: ${logEntry.error}`, 'WARN');
        });
      }

      return true;
    } else {
      log(`API调用失败 - HTTP状态码: ${response.statusCode}`, 'ERROR');
      log(`响应内容: ${response.body}`, 'ERROR');
      return false;
    }
  } catch (error) {
    log(`发送通知失败: ${error.message}`, 'ERROR');
    return false;
  }
}

// 主函数
async function main() {
  log('=== 开始执行微信通知发送任务 ===');

  try {
    // 检查服务状态
    log('检查服务状态...');
    if (!(await checkService())) {
      log(`错误: 微信通知服务未运行或无法访问 (${CONFIG.apiUrl})`, 'ERROR');
      log('=== 任务执行失败 ===', 'ERROR');
      process.exit(1);
    }

    log('服务状态检查通过');

    // 发送通知
    if (await sendNotifications()) {
      log('=== 任务执行成功 ===');
      process.exit(0);
    } else {
      log('=== 任务执行失败 ===', 'ERROR');
      process.exit(1);
    }
  } catch (error) {
    log(`任务执行异常: ${error.message}`, 'ERROR');
    log('=== 任务执行失败 ===', 'ERROR');
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  log(`未捕获的异常: ${error.message}`, 'ERROR');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  log(`未处理的Promise拒绝: ${reason}`, 'ERROR');
  process.exit(1);
});

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = { main, sendNotifications, checkService };
