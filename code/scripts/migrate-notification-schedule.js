const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 生成通知计划的函数
function generateNotificationSchedule(firstNotifyDate, monthlyNotifyDay, endDate = null, months = 12) {
  const schedule = [];
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // 添加首次通知日期（如果还未过期）
  if (firstNotifyDate >= today) {
    schedule.push(new Date(firstNotifyDate));
  }

  // 生成每月的通知日期
  const startDate = firstNotifyDate > today ? firstNotifyDate : today;
  const startYear = startDate.getFullYear();
  const startMonth = startDate.getMonth();

  for (let i = 0; i < months; i++) {
    const notifyDate = new Date(startYear, startMonth + i, monthlyNotifyDay);

    // 跳过已经过去的日期
    if (notifyDate < today) continue;

    // 如果设置了结束日期，跳过超出结束日期的通知
    if (endDate && notifyDate > endDate) break;

    // 避免重复添加首次通知日期
    if (!schedule.some(date =>
      date.getFullYear() === notifyDate.getFullYear() &&
      date.getMonth() === notifyDate.getMonth() &&
      date.getDate() === notifyDate.getDate()
    )) {
      schedule.push(new Date(notifyDate));
    }
  }

  return schedule.sort((a, b) => a.getTime() - b.getTime());
}

async function migrateNotificationSchedule() {
  try {
    console.log('开始迁移订阅者通知计划...');

    // 获取所有活跃的订阅者
    const subscribers = await prisma.subscriber.findMany({
      where: { isActive: true },
      include: {
        qrcode: true,
        scheduledNotifications: true,
      }
    });

    console.log(`找到 ${subscribers.length} 个活跃订阅者`);

    let migratedCount = 0;
    let skippedCount = 0;

    for (const subscriber of subscribers) {
      // 检查是否已经有通知计划
      if (subscriber.scheduledNotifications.length > 0) {
        console.log(`订阅者 ${subscriber.openid} 已有通知计划，跳过`);
        skippedCount++;
        continue;
      }

      if (!subscriber.qrcode) {
        console.log(`订阅者 ${subscriber.openid} 没有关联的二维码，跳过`);
        skippedCount++;
        continue;
      }

      // 生成通知计划
      const schedule = generateNotificationSchedule(
        subscriber.qrcode.firstNotifyDate,
        subscriber.qrcode.monthlyNotifyDay,
        subscriber.qrcode.endDate
      );

      if (schedule.length > 0) {
        // 批量创建通知计划
        let createdCount = 0;
        for (const date of schedule) {
          try {
            await prisma.scheduledNotification.create({
              data: {
                subscriberId: subscriber.id,
                scheduledDate: date,
              },
            });
            createdCount++;
          } catch (error) {
            // 忽略重复键错误，继续处理下一个
            if (!error.message.includes('Unique constraint')) {
              throw error;
            }
          }
        }

        console.log(`为订阅者 ${subscriber.openid} 创建了 ${createdCount} 个通知计划`);
        migratedCount++;
      } else {
        console.log(`订阅者 ${subscriber.openid} 没有需要创建的通知计划`);
        skippedCount++;
      }
    }

    console.log(`\n迁移完成！`);
    console.log(`成功迁移: ${migratedCount} 个订阅者`);
    console.log(`跳过: ${skippedCount} 个订阅者`);

  } catch (error) {
    console.error('迁移失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

migrateNotificationSchedule();
