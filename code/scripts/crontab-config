# 微信通知发送定时任务配置
# 每天凌晨4点执行通知发送

# 设置环境变量
PATH=/usr/local/bin:/usr/bin:/bin
SHELL=/bin/bash

# 每天凌晨4点发送通知 (使用Node.js脚本)
0 4 * * * /usr/bin/node /path/to/your/project/scripts/send-notifications.js >> /var/log/wechat-cron.log 2>&1

# 或者使用bash脚本 (二选一)
# 0 4 * * * /bin/bash /path/to/your/project/scripts/send-notifications.sh

# 可选: 每小时检查一次服务状态 (仅记录日志，不发送通知)
# 0 * * * * curl -s -o /dev/null -w "Service check at %{time_total}s - HTTP %{http_code}\n" http://127.0.0.1:3000/api/scheduler -X POST -H "x-scheduler-key: scheduler-secret-key" >> /var/log/wechat-service-check.log 2>&1

# 可选: 每周日凌晨2点清理旧日志
# 0 2 * * 0 find /var/log -name "wechat-*.log.old" -mtime +30 -delete

# 说明:
# - 0 4 * * * 表示每天凌晨4点执行
# - 如果需要修改时间，格式为: 分 时 日 月 星期
# - 例如: 0 6 * * * 表示每天早上6点执行
# - 例如: 30 3 * * * 表示每天凌晨3点30分执行
