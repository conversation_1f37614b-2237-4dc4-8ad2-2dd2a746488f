// JSON数据库初始化脚本
const fs = require('fs');
const path = require('path');

// 获取数据库目录路径
function getDatabasePath() {
  // 从环境变量中获取数据库目录
  require('dotenv').config();
  const dbDir = process.env.DATABASE_URL || './data';
  
  // 如果是相对路径，转换为绝对路径
  if (dbDir.startsWith('./') || dbDir.startsWith('../')) {
    return path.resolve(path.join(__dirname, '..'), dbDir);
  }
  return dbDir;
}

// 初始化数据库目录
function initializeDatabase() {
  console.log('正在初始化JSON数据库...');
  
  const dbDir = getDatabasePath();
  console.log(`数据库目录: ${dbDir}`);
  
  // 确保数据目录存在
  if (!fs.existsSync(dbDir)) {
    console.log(`创建数据目录: ${dbDir}`);
    fs.mkdirSync(dbDir, { recursive: true });
  }
  
  // 初始化各个数据文件
  const files = [
    'users.json',
    'templates.json',
    'qrcodes.json',
    'subscribers.json',
    'notificationLogs.json'
  ];
  
  files.forEach(file => {
    const filePath = path.join(dbDir, file);
    if (!fs.existsSync(filePath)) {
      console.log(`创建数据文件: ${file}`);
      fs.writeFileSync(filePath, JSON.stringify([]));
    } else {
      console.log(`数据文件已存在: ${file}`);
    }
  });
  
  // 检查管理员用户是否存在，如果不存在则创建
  const usersPath = path.join(dbDir, 'users.json');
  let users = [];
  
  try {
    const usersData = fs.readFileSync(usersPath, 'utf8');
    users = JSON.parse(usersData || '[]');
  } catch (error) {
    console.error('读取用户数据失败:', error.message);
    users = [];
  }
  
  // 如果没有用户，创建默认管理员用户
  if (users.length === 0) {
    console.log('创建默认管理员用户...');
    
    const bcrypt = require('bcryptjs');
    const adminUsername = process.env.ADMIN_USERNAME || 'admin';
    const adminPassword = process.env.ADMIN_PASSWORD || 'password';
    const hashedPassword = bcrypt.hashSync(adminPassword, 10);
    
    users.push({
      id: 1,
      username: adminUsername,
      password: hashedPassword
    });
    
    fs.writeFileSync(usersPath, JSON.stringify(users, null, 2));
    console.log(`已创建管理员用户: ${adminUsername}`);
  }
  
  console.log('JSON数据库初始化完成！');
  return true;
}

// 执行初始化
try {
  initializeDatabase();
} catch (error) {
  console.error('初始化JSON数据库失败:', error.message);
  process.exit(1);
}
