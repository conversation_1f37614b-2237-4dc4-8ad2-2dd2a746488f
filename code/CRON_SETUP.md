# 微信通知系统 Cron 任务设置指南

本指南将帮助你设置定时任务，每天凌晨4点自动发送微信通知。

## 快速开始

### 1. 自动安装（推荐）

```bash
# 进入项目目录
cd /path/to/your/wechat_notifier/code

# 运行安装脚本
./scripts/setup-cron.sh install
```

### 2. 手动安装

如果自动安装失败，可以手动设置：

```bash
# 1. 给脚本添加执行权限
chmod +x scripts/send-notifications.js

# 2. 测试脚本
node scripts/send-notifications.js

# 3. 编辑 crontab
crontab -e

# 4. 添加以下行（每天凌晨4点执行）
0 4 * * * /usr/bin/node /path/to/your/project/scripts/send-notifications.js >> /var/log/wechat-cron.log 2>&1
```

## 脚本说明

### Node.js 脚本 (推荐)
- **文件**: `scripts/send-notifications.js`
- **优点**: 跨平台兼容，错误处理完善，日志详细
- **依赖**: Node.js

### Bash 脚本
- **文件**: `scripts/send-notifications.sh`
- **优点**: 系统原生支持，无额外依赖
- **依赖**: curl, bash

## 时间配置

Cron 时间格式: `分 时 日 月 星期`

常用配置示例:
```bash
# 每天凌晨4点
0 4 * * *

# 每天早上6点
0 6 * * *

# 每天凌晨3点30分
30 3 * * *

# 每天上午9点和下午6点
0 9,18 * * *

# 仅工作日早上8点
0 8 * * 1-5
```

## 管理命令

```bash
# 查看当前 cron 任务
crontab -l

# 编辑 cron 任务
crontab -e

# 删除所有 cron 任务
crontab -r

# 查看执行日志
tail -f /var/log/wechat-cron.log

# 手动测试脚本
node scripts/send-notifications.js

# 检查任务状态
./scripts/setup-cron.sh status

# 卸载任务
./scripts/setup-cron.sh uninstall
```

## 日志管理

### 日志文件位置
- **Cron 执行日志**: `/var/log/wechat-cron.log`
- **应用详细日志**: `/var/log/wechat-notifications.log`

### 日志轮转
脚本会自动进行日志轮转，当日志文件超过10MB时会自动备份。

### 手动清理日志
```bash
# 清理30天前的旧日志
find /var/log -name "wechat-*.log.old" -mtime +30 -delete

# 清空当前日志
> /var/log/wechat-cron.log
```

## 故障排除

### 1. 权限问题
```bash
# 确保脚本有执行权限
chmod +x scripts/send-notifications.js

# 确保日志目录可写
sudo chmod 755 /var/log
sudo touch /var/log/wechat-cron.log
sudo chmod 644 /var/log/wechat-cron.log
```

### 2. 服务未运行
```bash
# 检查服务状态
curl -X POST http://127.0.0.1:3000/api/scheduler \
  -H "x-scheduler-key: scheduler-secret-key" \
  -H "Content-Type: application/json"

# 启动服务
npm run dev  # 开发环境
# 或
npm start    # 生产环境
```

### 3. 环境变量问题
确保 `.env` 文件包含正确的配置：
```env
SCHEDULER_SECRET_KEY="scheduler-secret-key"
WECHAT_APP_ID="your_app_id"
WECHAT_APP_SECRET="your_app_secret"
```

### 4. Node.js 路径问题
```bash
# 查找 Node.js 路径
which node

# 在 crontab 中使用完整路径
0 4 * * * /usr/local/bin/node /path/to/project/scripts/send-notifications.js
```

## 监控和报警

### 1. 添加健康检查
```bash
# 每小时检查服务状态
0 * * * * curl -s http://127.0.0.1:3000/health >> /var/log/health-check.log 2>&1
```

### 2. 邮件通知（可选）
```bash
# 发送失败时发送邮件
0 4 * * * /path/to/script || echo "微信通知发送失败" | mail -s "Alert" <EMAIL>
```

## 生产环境建议

1. **使用进程管理器**: PM2, systemd 等
2. **设置监控**: 日志监控、性能监控
3. **备份策略**: 定期备份数据库和配置
4. **安全配置**: 限制API访问、使用HTTPS
5. **负载均衡**: 多实例部署

## 示例配置

完整的生产环境 crontab 配置：
```bash
# 环境变量
PATH=/usr/local/bin:/usr/bin:/bin
SHELL=/bin/bash

# 每天凌晨4点发送通知
0 4 * * * /usr/bin/node /opt/wechat-notifier/scripts/send-notifications.js >> /var/log/wechat-cron.log 2>&1

# 每小时检查服务状态
0 * * * * curl -s -f http://127.0.0.1:3000/health || echo "Service down at $(date)" >> /var/log/service-alerts.log

# 每周日凌晨2点清理旧日志
0 2 * * 0 find /var/log -name "wechat-*.log.old" -mtime +30 -delete

# 每天凌晨1点备份数据库
0 1 * * * cp /opt/wechat-notifier/data.db /backup/wechat-$(date +\%Y\%m\%d).db
```
