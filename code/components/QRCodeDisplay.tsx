"use client";

import { useState, useEffect } from "react";
import Image from "next/image";

interface QRCodeDisplayProps {
  src: string;
  base64?: string;  // 优先使用base64数据
  alt: string;
  size: number;
  filename: string;
  className?: string;
}

export default function QRCodeDisplay({ src, base64, alt, size, filename, className = "" }: QRCodeDisplayProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  // 处理ESC键关闭模态框
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsModalOpen(false);
      }
    };

    if (isModalOpen) {
      document.addEventListener('keydown', handleEsc);
      // 防止背景滚动
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEsc);
      document.body.style.overflow = 'unset';
    };
  }, [isModalOpen]);

  // 下载图片函数
  const downloadImage = async (e: React.MouseEvent) => {
    e.stopPropagation();

    try {
      let downloadUrl: string;

      if (base64) {
        // 如果有base64数据，直接使用
        downloadUrl = base64;
      } else {
        // 否则从URL获取
        const response = await fetch(src);
        const blob = await response.blob();
        downloadUrl = window.URL.createObjectURL(blob);
      }

      // 清理文件名，移除特殊字符
      const cleanFilename = filename.replace(/[^a-zA-Z0-9\u4e00-\u9fa5_-]/g, '_');

      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${cleanFilename}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 只有从fetch创建的URL才需要释放
      if (!base64) {
        window.URL.revokeObjectURL(downloadUrl);
      }
    } catch (error) {
      console.error('下载图片失败:', error);
      alert('下载失败，请稍后重试');
    }
  };

  // 打开模态框
  const openModal = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsModalOpen(true);
  };

  // 关闭模态框
  const closeModal = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsModalOpen(false);
  };

  // 获取显示用的图片源
  const displaySrc = base64 || src;

  return (
    <>
      {/* 原始二维码显示 */}
      <div className={`relative inline-block group ${className}`}>
        <Image
          src={displaySrc}
          alt={alt}
          width={size}
          height={size}
          className="cursor-pointer hover:opacity-80 transition-opacity mx-auto"
          onClick={openModal}
        />

        {/* 下载按钮 */}
        <button
          onClick={downloadImage}
          className="absolute top-1 right-1 bg-blue-600 text-white p-1 rounded-full hover:bg-blue-700 transition-all duration-200 opacity-0 group-hover:opacity-100 shadow-lg"
          title="下载二维码"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M7 10L12 15L17 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M12 15V3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>

      {/* 模态框 */}
      {isModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={closeModal}
        >
          <div
            className="bg-white p-6 rounded-lg max-w-md mx-4 relative"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 关闭按钮 */}
            <button
              onClick={closeModal}
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 text-2xl"
              title="关闭"
            >
              ×
            </button>

            {/* 放大的二维码 */}
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-4">二维码</h3>
              <Image
                src={displaySrc}
                alt={alt}
                width={300}
                height={300}
                className="mx-auto mb-4"
              />

              {/* 下载按钮 */}
              <button
                onClick={downloadImage}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors flex items-center gap-2 mx-auto"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M7 10L12 15L17 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 15V3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                下载二维码
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
