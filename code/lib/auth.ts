import type { NextApiRequest, NextApiResponse, NextApiHandler } from "next";
import { verifyJwt } from "@/lib/jwt";

export function withAuth(handler: NextApiHandler) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const auth = req.headers.authorization;
    if (!auth || !auth.startsWith("Bearer ")) {
      return res.status(401).json({ error: "未认证" });
    }
    const token = auth.slice(7);
    const payload = verifyJwt(token);
    if (!payload) {
      return res.status(401).json({ error: "无效令牌" });
    }
    // @ts-ignore
    req.user = payload;
    return handler(req, res);
  };
}
