import axios from "axios";
import xml2js from "xml2js";

const WECHAT_APP_ID = process.env.WECHAT_APP_ID!;
const WECHAT_APP_SECRET = process.env.WECHAT_APP_SECRET!;
const WECHAT_TOKEN = process.env.WECHAT_TOKEN!;

let cachedToken: { value: string; expiresAt: number } | null = null;

export interface WeChatTemplate {
  template_id: string;
  title: string;
  primary_industry: string;
  deputy_industry: string;
  content: string;
  example: string;
}

export class WeChatAPI {
  static async getAccessToken(): Promise<string> {
    const now = Date.now();
    if (cachedToken && cachedToken.expiresAt > now) {
      return cachedToken.value;
    }
    const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${WECHAT_APP_ID}&secret=${WECHAT_APP_SECRET}`;
    const resp = await axios.get(url);
    if (resp.data.access_token) {
      cachedToken = {
        value: resp.data.access_token,
        expiresAt: now + (resp.data.expires_in - 60) * 1000,
      };
      return cachedToken.value;
    }
    throw new Error("获取微信 access_token 失败: " + JSON.stringify(resp.data));
  }

  static async createPermanentQRCode(sceneStr: string) {
    const token = await this.getAccessToken();
    const url = `https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=${token}`;
    const resp = await axios.post(url, {
      action_name: "QR_LIMIT_STR_SCENE",
      action_info: { scene: { scene_str: sceneStr } },
    });
    if (resp.data.ticket) {
      const imgUrl = `https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${encodeURIComponent(resp.data.ticket)}`;

      // 获取二维码图片并转换为base64
      let imgBase64 = null;
      try {
        const imgResp = await axios.get(imgUrl, { responseType: 'arraybuffer' });
        const buffer = Buffer.from(imgResp.data, 'binary');
        imgBase64 = `data:image/png;base64,${buffer.toString('base64')}`;
      } catch (error) {
        console.error('获取二维码图片失败:', error);
        // 如果获取图片失败，继续返回其他信息，但不包含base64
      }

      return {
        ticket: resp.data.ticket,
        url: resp.data.url,
        imgUrl,
        imgBase64,
      };
    }
    throw new Error("生成二维码失败: " + JSON.stringify(resp.data));
  }

  static async sendTemplateMessage(openid: string, templateId: string, data: any, url?: string) {
    const token = await this.getAccessToken();
    const api = `https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=${token}`;
    const payload: any = {
      touser: openid,
      template_id: templateId,
      data,
    };
    if (url) payload.url = url;
    const resp = await axios.post(api, payload);
    if (resp.data.errcode === 0) {
      return resp.data;
    }
    throw new Error("发送模板消息失败: " + JSON.stringify(resp.data));
  }

  static async getTemplateList(): Promise<WeChatTemplate[]> {
    const token = await this.getAccessToken();
    const url = `https://api.weixin.qq.com/cgi-bin/template/get_all_private_template?access_token=${token}`;
    const resp = await axios.get(url);
    if (resp.data.template_list) {
      return resp.data.template_list;
    }
    throw new Error("获取模板列表失败: " + JSON.stringify(resp.data));
  }

  static async parseXML(xml: string) {
    return xml2js.parseStringPromise(xml, { explicitArray: false });
  }
}
