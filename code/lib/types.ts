// 数据模型类型定义，与Prisma生成的类型保持一致

export interface User {
  id: number;
  username: string;
  password: string;
}

export interface Template {
  id: number;
  templateId: string;
  name: string;
  alias?: string | null;
  description?: string | null;
  variables: string;
  isActive: boolean;
}

export interface QRCode {
  id: number;
  name: string;
  sceneStr: string;
  templateId: number;
  templateData: string;
  firstNotifyDate: Date;
  monthlyNotifyDay: number;
  isActive: boolean;
  endDate?: Date | null;
  createdAt: Date;
  updatedAt: Date;
  // 二维码相关信息
  qrTicket?: string | null;
  qrUrl?: string | null;
  qrImgUrl?: string | null;
  qrImgBase64?: string | null;
  qrGeneratedAt?: Date | null;
}

export interface Subscriber {
  id: number;
  openid: string;
  qrcodeId: number;
  subscribedAt: Date;
  isActive: boolean;
}

export interface NotificationLog {
  id: number;
  subscriberId: number;
  sentAt: Date;
  status: string;
  error?: string | null;
}

export interface ScheduledNotification {
  id: number;
  subscriberId: number;
  scheduledDate: Date;
  actualSentAt?: Date | null;
  status: string;
  error?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// 包含关联数据的类型
export interface QRCodeWithRelations extends QRCode {
  template?: Template;
  subscribers?: Subscriber[];
}

export interface SubscriberWithRelations extends Subscriber {
  qrcode?: QRCode;
  notifications?: NotificationLog[];
  scheduledNotifications?: ScheduledNotification[];
}

export interface NotificationLogWithRelations extends NotificationLog {
  subscriber?: Subscriber;
}
