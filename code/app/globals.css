@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #3b82f6;
  --primary-dark: #2563eb;
  --secondary: #10b981;
  --secondary-dark: #059669;
  --accent: #8b5cf6;
  --accent-dark: #7c3aed;
  --success: #22c55e;
  --warning: #f59e0b;
  --danger: #ef4444;
  --background: #f9fafb;
  --foreground: #1f2937;
  --card: #ffffff;
  --card-foreground: #374151;
  --border: #e5e7eb;
  --input: #f3f4f6;
}

body {
  color: var(--foreground);
  background-color: var(--background);
}

@layer base {
  h1 {
    @apply text-2xl font-bold;
  }
  h2 {
    @apply text-xl font-semibold;
  }
  h3 {
    @apply text-lg font-medium;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded font-medium transition-colors;
  }
  .btn-primary {
    @apply bg-blue-500 hover:bg-blue-600 text-white;
  }
  .btn-secondary {
    @apply bg-emerald-500 hover:bg-emerald-600 text-white;
  }
  .btn-danger {
    @apply bg-red-500 hover:bg-red-600 text-white;
  }
  .card {
    @apply bg-white rounded-lg shadow p-6;
  }
  .form-input {
    @apply w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
