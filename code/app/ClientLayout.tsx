"use client";

import React, { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";

// 客户端布局组件，处理鉴权和用户信息
export default function ClientLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<{ username: string } | null>(null);

  useEffect(() => {
    const token = localStorage.getItem("token");
    console.log("ClientLayout: token =", token);
    if (!token) {
      console.log("ClientLayout: 未找到 token，跳转首页");
      router.push("/");
      setLoading(false);
      return;
    }
    fetch("/api/auth/me", {
      headers: { Authorization: "Bearer " + token },
    })
      .then((res) => {
        console.log("ClientLayout: /api/auth/me status =", res.status);
        if (!res.ok) throw new Error("未登录");
        return res.json();
      })
      .then((data) => {
        console.log("ClientLayout: /api/auth/me 返回 user =", data.user);
        setUser(data.user);
      })
      .catch((err) => {
        console.log("ClientLayout: /api/auth/me 失败", err);
        localStorage.removeItem("token");
        router.push("/");
      })
      .finally(() => {
        console.log("ClientLayout: setLoading(false)");
        setLoading(false);
      });
  }, [router]);

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div>加载中...</div>
      </div>
    );
  }

  // 导航项定义
  const navItems = [
    { name: "仪表盘", path: "/dashboard" },
    { name: "二维码管理", path: "/qrcodes" },
    { name: "订阅者管理", path: "/subscribers" },
    { name: "通知日志", path: "/notifications" },
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-blue-600 text-white shadow-md">
        <div className="container mx-auto px-4 py-3 flex justify-between items-center">
          <h1 className="text-xl font-bold">微信二维码通知系统</h1>
          {user && (
            <div className="flex items-center">
              <span className="mr-4">管理员: {user.username}</span>
              <button
                className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded transition-colors"
                onClick={() => {
                  localStorage.removeItem("token");
                  router.push("/");
                }}
              >
                登出
              </button>
            </div>
          )}
        </div>
      </header>

      {user && (
        <nav className="bg-white shadow-sm">
          <div className="container mx-auto px-4">
            <ul className="flex space-x-1">
              {navItems.map((item) => (
                <li key={item.path}>
                  <Link
                    href={item.path}
                    className={`inline-block px-4 py-3 font-medium transition-colors ${
                      pathname === item.path
                        ? "text-blue-600 border-b-2 border-blue-600"
                        : "text-gray-600 hover:text-blue-600 hover:bg-gray-50"
                    }`}
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </nav>
      )}

      <main className="flex-grow container mx-auto px-4 py-6">
        {children}
      </main>

      <footer className="bg-gray-100 border-t border-gray-200 py-4">
        <div className="container mx-auto px-4 text-center text-gray-500 text-sm">
          © {new Date().getFullYear()} 微信二维码通知系统
        </div>
      </footer>
    </div>
  );
}
