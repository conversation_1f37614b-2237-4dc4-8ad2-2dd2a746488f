"use client";

import { useEffect, useState } from "react";

interface NotificationLog {
  id: number;
  sentAt: string;
  status: string;
  error?: string;
  subscriber: {
    openid: string;
    qrcode: {
      name: string;
    };
  };
}

interface Stats {
  total: number;
  success: number;
  fail: number;
  successRate: number;
}

export default function NotificationsPage() {
  const [logs, setLogs] = useState<NotificationLog[]>([]);
  const [stats, setStats] = useState<Stats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    async function fetchNotifications() {
      try {
        const token = localStorage.getItem("token");
        const res = await fetch("/api/notifications", {
          headers: { Authorization: "Bearer " + token },
        });
        if (!res.ok) {
          setError("获取通知日志失败");
          setLoading(false);
          return;
        }
        const data = await res.json();
        setLogs(data.logs);
        setStats(data.stats);
      } catch {
        setError("网络错误");
      } finally {
        setLoading(false);
      }
    }
    fetchNotifications();
  }, []);

  if (loading) return <div>加载中...</div>;
  if (error) return <div className="text-red-600">{error}</div>;
  if (!stats) return null;

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6">通知日志</h2>
      <div className="mb-6 bg-white p-4 rounded shadow">
        <h3 className="text-lg font-semibold mb-2">统计信息</h3>
        <p>总数: {stats.total}</p>
        <p>成功: {stats.success}</p>
        <p>失败: {stats.fail}</p>
        <p>成功率: {stats.successRate}%</p>
      </div>
      <table className="min-w-full bg-white rounded shadow">
        <thead>
          <tr>
            <th className="border px-4 py-2">发送时间</th>
            <th className="border px-4 py-2">状态</th>
            <th className="border px-4 py-2">错误信息</th>
            <th className="border px-4 py-2">订阅者微信OpenID</th>
            <th className="border px-4 py-2">关联二维码</th>
          </tr>
        </thead>
        <tbody>
          {logs.map((log) => (
            <tr key={log.id}>
              <td className="border px-4 py-2">{new Date(log.sentAt).toLocaleString()}</td>
              <td className="border px-4 py-2">{log.status}</td>
              <td className="border px-4 py-2">{log.error || "-"}</td>
              <td className="border px-4 py-2">{log.subscriber.openid}</td>
              <td className="border px-4 py-2">{log.subscriber.qrcode?.name || "-"}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
