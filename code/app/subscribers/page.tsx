"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

interface Subscriber {
  id: number;
  openid: string;
  subscribedAt: string;
  isActive: boolean;
  qrcode: {
    name: string;
  };
}

export default function SubscribersPage() {
  const router = useRouter();
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    async function fetchSubscribers() {
      try {
        const token = localStorage.getItem("token");
        const res = await fetch("/api/subscribers", {
          headers: { Authorization: "Bearer " + token },
        });
        if (!res.ok) {
          setError("获取订阅者失败");
          setLoading(false);
          return;
        }
        const data = await res.json();
        setSubscribers(data.subscribers);
      } catch {
        setError("网络错误");
      } finally {
        setLoading(false);
      }
    }
    fetchSubscribers();
  }, []);

  if (loading) return <div>加载中...</div>;
  if (error) return <div className="text-red-600">{error}</div>;

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6">订阅者管理</h2>
      <table className="min-w-full bg-white rounded shadow">
        <thead>
          <tr>
            <th className="border px-4 py-2">微信OpenID</th>
            <th className="border px-4 py-2">关联二维码</th>
            <th className="border px-4 py-2">订阅时间</th>
            <th className="border px-4 py-2">状态</th>
            <th className="border px-4 py-2">操作</th>
          </tr>
        </thead>
        <tbody>
          {subscribers.map((sub) => (
            <tr key={sub.id} className="hover:bg-gray-100 cursor-pointer" onClick={() => router.push(`/subscribers/${sub.id}`)}>
              <td className="border px-4 py-2">{sub.openid}</td>
              <td className="border px-4 py-2">{sub.qrcode?.name || "-"}</td>
              <td className="border px-4 py-2">{new Date(sub.subscribedAt).toLocaleString()}</td>
              <td className="border px-4 py-2">{sub.isActive ? "激活" : "停用"}</td>
              <td className="border px-4 py-2 text-blue-600 underline">查看 / 编辑</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
