"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";

interface ScheduledNotification {
  id: number;
  scheduledDate: string;
  actualSentAt?: string;
  status: string;
  error?: string;
}

interface Subscriber {
  id: number;
  openid: string;
  subscribedAt: string;
  isActive: boolean;
  qrcode: {
    name: string;
  };
  notifications: {
    id: number;
    sentAt: string;
    status: string;
    error?: string;
  }[];
  scheduledNotifications: ScheduledNotification[];
}

export default function SubscriberDetailPage() {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;

  const [subscriber, setSubscriber] = useState<Subscriber | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [sendingTest, setSendingTest] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  useEffect(() => {
    async function fetchSubscriber() {
      try {
        const token = localStorage.getItem("token");
        const res = await fetch(`/api/subscribers/${id}`, {
          headers: { Authorization: "Bearer " + token },
        });
        if (!res.ok) {
          setError("获取订阅者失败");
          setLoading(false);
          return;
        }
        const data = await res.json();
        setSubscriber(data.subscriber);
      } catch {
        setError("网络错误");
      } finally {
        setLoading(false);
      }
    }
    fetchSubscriber();
  }, [id]);

  const handleToggleActive = async () => {
    if (!subscriber) return;
    setError("");
    setSaving(true);
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(`/api/subscribers/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json", Authorization: "Bearer " + token },
        body: JSON.stringify({ isActive: !subscriber.isActive }),
      });
      if (!res.ok) {
        const data = await res.json();
        setError(data.error || "操作失败");
        setSaving(false);
        return;
      }
      setSubscriber({ ...subscriber, isActive: !subscriber.isActive });
    } catch {
      setError("网络错误");
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm("确认删除该订阅者？此操作不可撤销")) return;
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(`/api/subscribers/${id}`, {
        method: "DELETE",
        headers: { Authorization: "Bearer " + token },
      });
      if (!res.ok) {
        const data = await res.json();
        setError(data.error || "删除失败");
        return;
      }
      router.push("/subscribers");
    } catch {
      setError("网络错误");
    }
  };

  const handleSendTest = async () => {
    if (!confirm("确定要发送测试消息吗？")) return;
    setSendingTest(true);
    setError("");
    setSuccessMessage("");

    try {
      const token = localStorage.getItem("token");
      const res = await fetch(`/api/subscribers/${id}/send-test`, {
        method: "POST",
        headers: {
          Authorization: "Bearer " + token,
          "Content-Type": "application/json"
        },
      });

      const data = await res.json();

      if (!res.ok) {
        setError(data.error || "发送测试消息失败");
        return;
      }

      setSuccessMessage(`测试消息发送成功！项目：${data.data.projectName}，还款日期：${data.data.paymentDate}，金额：${data.data.amount}元`);

      // 刷新订阅者数据以更新通知历史
      const fetchRes = await fetch(`/api/subscribers/${id}`, {
        headers: { Authorization: "Bearer " + token },
      });
      if (fetchRes.ok) {
        const fetchData = await fetchRes.json();
        setSubscriber(fetchData.subscriber);
      }

    } catch {
      setError("网络错误");
    } finally {
      setSendingTest(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "sent":
        return "text-green-600 bg-green-100";
      case "pending":
        return "text-yellow-600 bg-yellow-100";
      case "failed":
        return "text-red-600 bg-red-100";
      case "cancelled":
        return "text-gray-600 bg-gray-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "sent":
        return "已发送";
      case "pending":
        return "待发送";
      case "failed":
        return "发送失败";
      case "cancelled":
        return "已取消";
      default:
        return status;
    }
  };

  if (loading) return <div>加载中...</div>;
  if (error) return <div className="text-red-600 mb-4">{error}</div>;
  if (!subscriber) return null;

  return (
    <main className="max-w-3xl mx-auto p-6 bg-white rounded shadow space-y-4">
      <h2 className="text-2xl font-bold">订阅者详情</h2>
      {error && <div className="text-red-600 mb-4">{error}</div>}
      {successMessage && <div className="text-green-600 mb-4">{successMessage}</div>}
      <div>
        <strong>微信OpenID:</strong> {subscriber.openid}
      </div>
      <div>
        <strong>关联二维码:</strong> {subscriber.qrcode?.name || "-"}
      </div>
      <div>
        <strong>订阅时间:</strong> {new Date(subscriber.subscribedAt).toLocaleString()}
      </div>
      <div>
        <strong>状态:</strong> {subscriber.isActive ? "激活" : "停用"}
      </div>
      <div className="flex space-x-4 mt-4">
        <button
          onClick={handleToggleActive}
          disabled={saving}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {saving ? "处理中..." : subscriber.isActive ? "停用" : "激活"}
        </button>
        <button
          onClick={handleSendTest}
          disabled={sendingTest || !subscriber.isActive}
          className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
        >
          {sendingTest ? "发送中..." : "发送测试消息"}
        </button>
        <button
          onClick={handleDelete}
          className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
        >
          删除
        </button>
      </div>
      {/* 通知日历 */}
      <div>
        <h3 className="mt-6 font-semibold">通知日历</h3>
        <div className="mt-2 bg-gray-50 rounded p-4">
          {subscriber.scheduledNotifications && subscriber.scheduledNotifications.length > 0 ? (
            <div className="grid gap-2">
              {subscriber.scheduledNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className="flex items-center justify-between p-3 bg-white rounded border"
                >
                  <div className="flex items-center space-x-4">
                    <div className="text-sm font-medium">
                      {new Date(notification.scheduledDate).toLocaleDateString('zh-CN', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        weekday: 'short'
                      })}
                    </div>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(notification.status)}`}
                    >
                      {getStatusText(notification.status)}
                    </span>
                  </div>
                  <div className="text-sm text-gray-500">
                    {notification.actualSentAt && (
                      <span>
                        实际发送: {new Date(notification.actualSentAt).toLocaleString()}
                      </span>
                    )}
                    {notification.error && (
                      <span className="text-red-500 ml-2">
                        错误: {notification.error}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">暂无通知计划</p>
          )}
        </div>
      </div>

      {/* 通知历史 */}
      <div>
        <h3 className="mt-6 font-semibold">通知历史</h3>
        <table className="min-w-full bg-white rounded shadow mt-2">
          <thead>
            <tr>
              <th className="border px-4 py-2">发送时间</th>
              <th className="border px-4 py-2">状态</th>
              <th className="border px-4 py-2">错误信息</th>
            </tr>
          </thead>
          <tbody>
            {subscriber.notifications.map((log) => (
              <tr key={log.id}>
                <td className="border px-4 py-2">{new Date(log.sentAt).toLocaleString()}</td>
                <td className="border px-4 py-2">{log.status}</td>
                <td className="border px-4 py-2">{log.error || "-"}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </main>
  );
}
