"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  WECHAT_TEMPLATE,
  calculateReminderDate,
  formatDate,
  formatChineseDate,
  generateSceneStr,
  generateQRCodeName,
  calculateFutureReminderDates,
  getNextPaymentDate,
  STORAGE_KEYS
} from "@/lib/config";

export default function QRCodeNewPage() {
  const router = useRouter();
  const [projectName, setProjectName] = useState("");
  const [paymentDay, setPaymentDay] = useState(new Date().getDate()); // 默认为今天的日期
  const [amount, setAmount] = useState("");
  const [advanceDays, setAdvanceDays] = useState(WECHAT_TEMPLATE.defaultAdvanceDays);
  const [monthsCount, setMonthsCount] = useState(1);
  const [error, setError] = useState("");
  const [submitting, setSubmitting] = useState(false);

  // 自动生成的场景字符串和名称
  const sceneStr = projectName ? generateSceneStr(projectName, paymentDay, amount) : "";
  const name = projectName ? generateQRCodeName(projectName, paymentDay, amount) : "";

  // 从本地存储加载上次的值
  useEffect(() => {
    const savedProjectName = localStorage.getItem(STORAGE_KEYS.LAST_PROJECT_NAME);
    const savedAmount = localStorage.getItem(STORAGE_KEYS.LAST_AMOUNT);
    const savedMonthsCount = localStorage.getItem(STORAGE_KEYS.LAST_MONTHS_COUNT);
    const savedAdvanceDays = localStorage.getItem(STORAGE_KEYS.LAST_ADVANCE_DAYS);

    if (savedProjectName) {
      setProjectName(savedProjectName);
    }

    if (savedAmount) {
      setAmount(savedAmount);
    }

    if (savedMonthsCount) {
      setMonthsCount(parseInt(savedMonthsCount));
    }

    if (savedAdvanceDays) {
      setAdvanceDays(parseInt(savedAdvanceDays));
    }
  }, []);

  // 计算提醒日期列表
  const reminderDates = [];
  if (paymentDay > 0) {
    const reminderDate = calculateReminderDate(paymentDay, advanceDays);
    reminderDates.push(formatDate(reminderDate));
  }

  // 计算未来几个月的提醒日期
  const futureReminderDates = paymentDay > 0 && monthsCount > 0
    ? calculateFutureReminderDates(paymentDay, monthsCount, advanceDays)
    : [];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSubmitting(true);

    if (!projectName || !amount || paymentDay < 1) {
      setError("请填写所有必填项");
      setSubmitting(false);
      return;
    }

    // 保存当前值到本地存储
    localStorage.setItem(STORAGE_KEYS.LAST_PROJECT_NAME, projectName);
    localStorage.setItem(STORAGE_KEYS.LAST_AMOUNT, amount);
    localStorage.setItem(STORAGE_KEYS.LAST_MONTHS_COUNT, monthsCount.toString());
    localStorage.setItem(STORAGE_KEYS.LAST_ADVANCE_DAYS, advanceDays.toString());

    try {
      const token = localStorage.getItem("token");
      const reminderDate = calculateReminderDate(paymentDay, advanceDays);

      const res = await fetch("/api/qrcodes", {
        method: "POST",
        headers: { "Content-Type": "application/json", Authorization: "Bearer " + token },
        body: JSON.stringify({
          name,
          sceneStr,
          projectName,
          paymentDay,
          amount,
          advanceDays,
          notifyCount: monthsCount,
          firstNotifyDate: formatDate(reminderDate),
        }),
      });
      if (!res.ok) {
        const data = await res.json();
        setError(data.error || "创建失败");
        setSubmitting(false);
        return;
      }
      router.push("/qrcodes");
    } catch {
      setError("网络错误");
      setSubmitting(false);
    }
  };

  // 获取下一次还款日期用于预览
  const nextPaymentDate = paymentDay > 0 ? getNextPaymentDate(paymentDay) : null;
  const formattedNextPaymentDate = nextPaymentDate ? formatChineseDate(nextPaymentDate) : '';

  return (
    <main className="max-w-3xl mx-auto p-6 bg-white rounded shadow">
      <h2 className="text-2xl font-bold mb-6">新建租金提醒</h2>
      {error && <div className="mb-4 text-red-600 font-semibold">{error}</div>}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block mb-1 font-medium">项目名称</label>
          <input
            type="text"
            className="w-full border border-gray-300 rounded px-3 py-2"
            value={projectName}
            onChange={(e) => setProjectName(e.target.value)}
            required
          />
          <p className="text-sm text-gray-500 mt-1">将显示在微信通知中</p>
        </div>
        <div>
          <label className="block mb-1 font-medium">每月还款日</label>
          <input
            type="number"
            min={1}
            max={28}
            className="w-full border border-gray-300 rounded px-3 py-2"
            value={paymentDay}
            onChange={(e) => setPaymentDay(Number(e.target.value))}
            required
          />
        </div>
        <div>
          <label className="block mb-1 font-medium">应付租金</label>
          <input
            type="text"
            className="w-full border border-gray-300 rounded px-3 py-2"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            required
          />
          <p className="text-sm text-gray-500 mt-1">将显示在微信通知中</p>
        </div>
        <div>
          <label className="block mb-1 font-medium">提前提醒天数</label>
          <input
            type="number"
            min={1}
            max={10}
            className="w-full border border-gray-300 rounded px-3 py-2"
            value={advanceDays}
            onChange={(e) => setAdvanceDays(Number(e.target.value))}
            required
          />
        </div>
        <div>
          <label className="block mb-1 font-medium">发送月数</label>
          <input
            type="number"
            min={1}
            max={24}
            className="w-full border border-gray-300 rounded px-3 py-2"
            value={monthsCount}
            onChange={(e) => setMonthsCount(Number(e.target.value))}
            required
          />
          <p className="text-sm text-gray-500 mt-1">每月发送一次，共发送几个月</p>
        </div>

        {futureReminderDates.length > 0 && (
          <div className="p-3 bg-green-50 rounded mt-2">
            <h3 className="font-medium mb-2">未来{monthsCount}个月的提醒日期</h3>
            <ul className="list-disc pl-5">
              {futureReminderDates.map((date, index) => (
                <li key={index}>{formatDate(date)}</li>
              ))}
            </ul>
          </div>
        )}

        <div className="p-3 bg-gray-50 rounded">
          <h3 className="font-medium mb-2">二维码信息</h3>
          <p><strong>名称:</strong> {name}</p>
          <p><strong>场景字符串:</strong> {sceneStr}</p>
        </div>

        <div className="p-3 bg-yellow-50 rounded">
          <h3 className="font-medium mb-2">消息预览</h3>
          <div className="border border-gray-200 p-4 rounded bg-white">
            <div className="font-bold text-lg mb-2">{WECHAT_TEMPLATE.title}</div>
            <div className="space-y-2">
              <div>
                <span className="text-gray-500">项目名称：</span>
                <span>{projectName || '(未填写)'}</span>
              </div>
              <div>
                <span className="text-gray-500">还款日期：</span>
                <span>{formattedNextPaymentDate || '(未填写)'}</span>
              </div>
              <div>
                <span className="text-gray-500">应付租金：</span>
                <span>{amount || '(未填写)'}</span>
              </div>
            </div>
          </div>
        </div>

        <button
          type="submit"
          disabled={submitting}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {submitting ? "创建中..." : "创建"}
        </button>
      </form>
    </main>
  );
}
