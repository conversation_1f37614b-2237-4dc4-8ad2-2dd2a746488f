"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import QRCodeDisplay from "@/components/QRCodeDisplay";
import {
  WECHAT_TEMPLATE,
  calculateReminderDate,
  formatDate,
  formatChineseDate,
  calculateFutureReminderDates,
  generateQRCodeName,
  getNextPaymentDate,
  STORAGE_KEYS,
  generateTemplateData
} from "@/lib/config";

interface Template {
  id: number;
  templateId: string;
  name: string;
  alias?: string;
  description?: string;
  variables: string;
  isActive: boolean;
}

interface QRCode {
  id: number;
  name: string;
  sceneStr: string;
  templateId: number;
  template: Template;
  templateData: string;
  firstNotifyDate: string;
  monthlyNotifyDay: number;
  isActive: boolean;
  endDate?: string | null;
  subscribers: any[];
}

export default function QRCodeEditPage() {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;

  const [qrcode, setQRCode] = useState<QRCode | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");
  const [message, setMessage] = useState("");
  const [qrImageUrl, setQrImageUrl] = useState("");
  const [qrImageBase64, setQrImageBase64] = useState("");
  const [qrExpired, setQrExpired] = useState(false);

  // 从模板数据中提取项目名称、还款日期和金额
  const [projectName, setProjectName] = useState("");
  const [amount, setAmount] = useState("");
  const [paymentDay, setPaymentDay] = useState(new Date().getDate());
  const [advanceDays, setAdvanceDays] = useState(WECHAT_TEMPLATE.defaultAdvanceDays);
  const [notifyCount, setNotifyCount] = useState(1);

  // 计算提醒日期列表
  const reminderDates = [];
  if (qrcode && paymentDay > 0) {
    const reminderDate = calculateReminderDate(paymentDay, advanceDays);
    reminderDates.push(formatDate(reminderDate));
  }

  // 计算未来几个月的提醒日期
  const futureReminderDates = qrcode && paymentDay > 0 && notifyCount > 0
    ? calculateFutureReminderDates(paymentDay, notifyCount, advanceDays)
    : [];

  useEffect(() => {
    async function fetchData() {
      try {
        const token = localStorage.getItem("token");
        const qrRes = await fetch(`/api/qrcodes/${id}`, {
          headers: { Authorization: "Bearer " + token }
        });

        if (!qrRes.ok) {
          setError("获取数据失败");
          setLoading(false);
          return;
        }

        const qrData = await qrRes.json();
        const qrcodeData = qrData.qrcode;

        // 设置二维码数据
        setQRCode({
          ...qrcodeData,
          firstNotifyDate: qrcodeData.firstNotifyDate.slice(0, 10),
          endDate: qrcodeData.endDate ? qrcodeData.endDate.slice(0, 10) : "",
        });

        // 如果有二维码图片URL，设置它
        if (qrcodeData.qrImgUrl) {
          setQrImageUrl(qrcodeData.qrImgUrl);
          if (qrcodeData.qrImgBase64) {
            setQrImageBase64(qrcodeData.qrImgBase64);
          }

          // 检查二维码是否过期或即将过期
          if (qrcodeData.qrGeneratedAt) {
            const generatedAt = new Date(qrcodeData.qrGeneratedAt);
            const now = new Date();
            const daysDiff = Math.floor((now.getTime() - generatedAt.getTime()) / (1000 * 60 * 60 * 24));

            // 如果超过29天，标记为过期
            if (daysDiff >= 29) {
              setQrExpired(true);
              setMessage("二维码已过期，请点击'生成二维码'按钮重新生成");
            }
            // 如果超过25天但不到29天，提示即将过期
            else if (daysDiff >= 25) {
              setMessage(`二维码将在${29 - daysDiff}天后过期，建议重新生成`);
            }
          }
        }

        // 解析模板数据
        if (qrcodeData.templateData) {
          try {
            const templateData = JSON.parse(qrcodeData.templateData);
            if (templateData.thing12 && templateData.thing12.value) {
              setProjectName(templateData.thing12.value);
            }
            if (templateData.amount3 && templateData.amount3.value) {
              setAmount(templateData.amount3.value);
            }
          } catch (e) {
            console.error("解析模板数据失败", e);
          }
        }

        // 设置其他字段
        setPaymentDay(qrcodeData.monthlyNotifyDay);
        setAdvanceDays(WECHAT_TEMPLATE.defaultAdvanceDays);
        setNotifyCount(12); // 默认12个月
      } catch {
        setError("网络错误");
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, [id]);

  const handleSave = async () => {
    if (!qrcode) return;
    setError("");
    setSaving(true);

    if (!projectName || !amount || paymentDay < 1 || paymentDay > 28) {
      setError("请填写所有必填项，还款日必须在1-28之间");
      setSaving(false);
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const reminderDate = calculateReminderDate(paymentDay, advanceDays);

      // 保存当前值到本地存储
      localStorage.setItem(STORAGE_KEYS.LAST_PROJECT_NAME, projectName);
      localStorage.setItem(STORAGE_KEYS.LAST_AMOUNT, amount);
      localStorage.setItem(STORAGE_KEYS.LAST_MONTHS_COUNT, notifyCount.toString());
      localStorage.setItem(STORAGE_KEYS.LAST_ADVANCE_DAYS, advanceDays.toString());

      // 生成新的名称（基于当前项目名称、还款日和金额）
      const newName = generateQRCodeName(projectName, paymentDay, amount);

      // 生成模板数据
      const templateData = JSON.stringify(generateTemplateData(
        projectName,
        new Date(reminderDate).toISOString().split('T')[0],
        amount
      ));

      const res = await fetch(`/api/qrcodes/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json", Authorization: "Bearer " + token },
        body: JSON.stringify({
          name: newName, // 使用新生成的名称
          monthlyNotifyDay: paymentDay,
          firstNotifyDate: formatDate(reminderDate),
          templateData,
          isActive: qrcode.isActive,
        }),
      });
      if (!res.ok) {
        const data = await res.json();
        setError(data.error || "保存失败");
        setSaving(false);
        return;
      }
      router.push("/qrcodes");
    } catch {
      setError("网络错误");
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm("确认删除该二维码？此操作不可撤销")) return;
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(`/api/qrcodes/${id}`, {
        method: "DELETE",
        headers: { Authorization: "Bearer " + token },
      });
      if (!res.ok) {
        const data = await res.json();
        setError(data.error || "删除失败");
        return;
      }
      router.push("/qrcodes");
    } catch {
      setError("网络错误");
    }
  };

  const handleGenerateQRCode = async () => {
    if (!qrcode) return;
    setError("");
    try {
      const token = localStorage.getItem("token");
      const res = await fetch("/api/qrcodes/generate", {
        method: "POST",
        headers: { "Content-Type": "application/json", Authorization: "Bearer " + token },
        body: JSON.stringify({
          sceneStr: qrcode.sceneStr,
          qrcodeId: qrcode.id
        }),
      });
      if (!res.ok) {
        const data = await res.json();
        setError(data.error || "生成二维码失败");
        return;
      }
      const data = await res.json();

      // 重置过期状态
      setQrExpired(false);

      // 设置二维码图片URL和base64数据
      setQrImageUrl(data.qr.imgUrl);
      if (data.qr.imgBase64) {
        setQrImageBase64(data.qr.imgBase64);
      }

      // 显示是否重新生成的消息
      if (data.regenerated) {
        setMessage("二维码已重新生成");
      } else {
        setMessage("使用已有的二维码");
      }
    } catch {
      setError("网络错误");
    }
  };

  // 获取下一次还款日期用于预览
  const nextPaymentDate = paymentDay > 0 ? getNextPaymentDate(paymentDay) : null;
  const formattedNextPaymentDate = nextPaymentDate ? formatChineseDate(nextPaymentDate) : '';

  if (loading) return <div>加载中...</div>;
  if (error) return <div className="text-red-600 mb-4">{error}</div>;
  if (!qrcode) return null;

  return (
    <main className="max-w-3xl mx-auto p-6 bg-white rounded shadow space-y-4">
      <h2 className="text-2xl font-bold">编辑租金提醒</h2>
      <div className="p-3 bg-gray-50 rounded">
        <h3 className="font-medium mb-2">二维码信息</h3>
        <p><strong>名称:</strong> {qrcode.name}</p>
        <p><strong>场景字符串:</strong> {qrcode.sceneStr}</p>
      </div>
      <div>
        <label className="block mb-1 font-medium">项目名称</label>
        <input
          type="text"
          className="w-full border border-gray-300 rounded px-3 py-2"
          value={projectName}
          onChange={(e) => setProjectName(e.target.value)}
          required
        />
        <p className="text-sm text-gray-500 mt-1">将显示在微信通知中</p>
      </div>
      <div>
        <label className="block mb-1 font-medium">每月还款日</label>
        <input
          type="number"
          min={1}
          max={28}
          className="w-full border border-gray-300 rounded px-3 py-2"
          value={paymentDay}
          onChange={(e) => setPaymentDay(Number(e.target.value))}
          required
        />
      </div>
      <div>
        <label className="block mb-1 font-medium">应付租金</label>
        <input
          type="text"
          className="w-full border border-gray-300 rounded px-3 py-2"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          required
        />
        <p className="text-sm text-gray-500 mt-1">将显示在微信通知中</p>
      </div>
      <div>
        <label className="block mb-1 font-medium">提前提醒天数</label>
        <input
          type="number"
          min={1}
          max={10}
          className="w-full border border-gray-300 rounded px-3 py-2"
          value={advanceDays}
          onChange={(e) => setAdvanceDays(Number(e.target.value))}
          required
        />
      </div>
      <div>
        <label className="block mb-1 font-medium">发送月数</label>
        <input
          type="number"
          min={1}
          max={24}
          className="w-full border border-gray-300 rounded px-3 py-2"
          value={notifyCount}
          onChange={(e) => setNotifyCount(Number(e.target.value))}
          required
        />
        <p className="text-sm text-gray-500 mt-1">每月发送一次，共发送几个月</p>
      </div>
      {futureReminderDates.length > 0 && (
        <div className="p-3 bg-green-50 rounded mt-2">
          <h3 className="font-medium mb-2">未来{notifyCount}个月的提醒日期</h3>
          <ul className="list-disc pl-5">
            {futureReminderDates.map((date, index) => (
              <li key={index}>{formatDate(date)}</li>
            ))}
          </ul>
        </div>
      )}

      <div className="p-3 bg-yellow-50 rounded">
        <h3 className="font-medium mb-2">消息预览</h3>
        <div className="border border-gray-200 p-4 rounded bg-white">
          <div className="font-bold text-lg mb-2">{WECHAT_TEMPLATE.title}</div>
          <div className="space-y-2">
            <div>
              <span className="text-gray-500">项目名称：</span>
              <span>{projectName}</span>
            </div>
            <div>
              <span className="text-gray-500">还款日期：</span>
              <span>{formattedNextPaymentDate || '(未填写)'}</span>
            </div>
            <div>
              <span className="text-gray-500">应付租金：</span>
              <span>{amount}</span>
            </div>
          </div>
        </div>
      </div>
      <div className="flex space-x-4">
        <button
          onClick={handleSave}
          disabled={saving}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {saving ? "保存中..." : "保存"}
        </button>
        <button
          onClick={handleDelete}
          className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
        >
          删除
        </button>
        <button
          onClick={handleGenerateQRCode}
          className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
        >
          生成二维码
        </button>
      </div>
      {message && (
        <div className="p-3 bg-blue-50 rounded">
          <p>{message}</p>
        </div>
      )}
      {qrImageUrl && (
        <div>
          <h3 className="mt-4 font-semibold">二维码预览</h3>
          <div className="relative">
            <QRCodeDisplay
              src={qrImageUrl}
              base64={qrImageBase64}
              alt="二维码"
              size={192}
              filename={`qrcode-${qrcode.name}`}
              className="mt-2"
            />
            {qrExpired && (
              <div className="absolute inset-0 flex items-center justify-center bg-red-500 bg-opacity-50 text-white font-bold">
                已过期
              </div>
            )}
          </div>
        </div>
      )}
    </main>
  );
}
