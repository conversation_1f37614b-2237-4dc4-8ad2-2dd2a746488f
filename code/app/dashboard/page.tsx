"use client";

import { useEffect, useState } from "react";

interface Stats {
  qrcodeCount: number;
  subscriberCount: number;
  notificationTotal: number;
  notificationSuccess: number;
  notificationFail: number;
  notificationSuccessRate: number;
}

export default function DashboardPage() {
  const [stats, setStats] = useState<Stats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    async function fetchStats() {
      try {
        const token = localStorage.getItem("token");
        const headers = { Authorization: `Bearer ${token}` };

        const [qrcodesRes, subscribersRes, notificationsRes] = await Promise.all([
          fetch("/api/qrcodes", { headers }),
          fetch("/api/subscribers", { headers }),
          fetch("/api/notifications", { headers }),
        ]);
        if (!qrcodesRes.ok || !subscribersRes.ok || !notificationsRes.ok) {
          setError("获取数据失败");
          setLoading(false);
          return;
        }
        const qrcodesData = await qrcodesRes.json();
        const subscribersData = await subscribersRes.json();
        const notificationsData = await notificationsRes.json();

        setStats({
          qrcodeCount: qrcodesData.qrcodes.length,
          subscriberCount: subscribersData.subscribers.length,
          notificationTotal: notificationsData.stats.total,
          notificationSuccess: notificationsData.stats.success,
          notificationFail: notificationsData.stats.fail,
          notificationSuccessRate: notificationsData.stats.successRate,
        });
      } catch {
        setError("网络错误");
      } finally {
        setLoading(false);
      }
    }
    fetchStats();
  }, []);

  if (loading) {
    return <div>加载中...</div>;
  }

  if (error) {
    return <div className="text-red-600">{error}</div>;
  }

  if (!stats) {
    return null;
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">系统仪表盘</h2>
        <div className="text-sm text-gray-500">
          最后更新: {new Date().toLocaleString()}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-6 rounded-lg shadow-lg">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-medium mb-1 opacity-90">二维码</h3>
              <p className="text-3xl font-bold">{stats.qrcodeCount}</p>
            </div>
            <div className="bg-blue-400 bg-opacity-30 p-3 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-emerald-500 to-emerald-600 text-white p-6 rounded-lg shadow-lg">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-medium mb-1 opacity-90">订阅者</h3>
              <p className="text-3xl font-bold">{stats.subscriberCount}</p>
            </div>
            <div className="bg-emerald-400 bg-opacity-30 p-3 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-500 to-purple-600 text-white p-6 rounded-lg shadow-lg">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-medium mb-1 opacity-90">通知成功率</h3>
              <p className="text-3xl font-bold">{stats.notificationSuccessRate}%</p>
            </div>
            <div className="bg-purple-400 bg-opacity-30 p-3 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold mb-4 border-b pb-2">通知发送详情</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="p-4 bg-gray-50 rounded-md">
            <div className="text-sm text-gray-500 mb-1">总通知数</div>
            <div className="text-2xl font-semibold">{stats.notificationTotal}</div>
          </div>
          <div className="p-4 bg-green-50 rounded-md">
            <div className="text-sm text-gray-500 mb-1">成功发送</div>
            <div className="text-2xl font-semibold text-green-600">{stats.notificationSuccess}</div>
          </div>
          <div className="p-4 bg-red-50 rounded-md">
            <div className="text-sm text-gray-500 mb-1">发送失败</div>
            <div className="text-2xl font-semibold text-red-600">{stats.notificationFail}</div>
          </div>
          <div className="p-4 bg-blue-50 rounded-md">
            <div className="text-sm text-gray-500 mb-1">成功率</div>
            <div className="text-2xl font-semibold text-blue-600">{stats.notificationSuccessRate}%</div>
          </div>
        </div>
      </div>
    </div>
  );
}
