// ecosystem.config.js
module.exports = {
  apps : [{
    name: "wechat-notifier", // 你的应用名称
    script: "npm",
    args: "start",
    // cwd: "/path/to/your/nextjs/app", // 你的 Next.js 项目路径
    instances: 1, // 启动的实例数量，可以是 'max' 根据CPU核心数自动调整
    autorestart: true, // 崩溃后自动重启
    watch: false, // 生产环境不建议开启文件监控
    max_memory_restart: '1500M', // 内存占用超过1G自动重启 (根据实际情况调整)
    env: {
      NODE_ENV: "production",
    },
  }]
};