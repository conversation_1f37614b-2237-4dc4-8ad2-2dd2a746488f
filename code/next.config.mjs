/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  // 确保自定义服务器和数据库检查脚本被包含在 standalone 输出中
  experimental: {
    outputFileTracingRoot: process.cwd(),
    outputFileTracingIncludes: {
      "/**": ["./scripts/**/*", "./server.js", "./prisma/**/*"],
    },
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "mp.weixin.qq.com",
        port: "",
        pathname: "/**",
      },
    ],
  },
  //忽略typescript错误
  typescript: {
    ignoreBuildErrors: true,
  },
};

export default nextConfig;
