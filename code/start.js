#!/usr/bin/env node

// 启动脚本，用于启动自定义服务器
// 这个脚本可以在 standalone 构建后使用: node start.js

const path = require('path');
const fs = require('fs');
const { execSync, spawn } = require('child_process');

// 检查环境变量
function loadEnv() {
  try {
    const dotenv = require('dotenv');
    const envPath = path.resolve(process.cwd(), '.env');

    if (fs.existsSync(envPath)) {
      console.log('加载 .env 文件...');
      dotenv.config({ path: envPath });
    } else {
      console.log('未找到 .env 文件，使用默认环境变量');
    }
  } catch (error) {
    console.warn('加载环境变量失败:', error.message);
  }
}

// 检查并初始化数据库
function checkDatabase() {
  console.log('正在检查数据库状态...');

  try {
    // 检查脚本是否存在
    const scriptPath = path.resolve(process.cwd(), 'scripts/init-jsondb.js');
    if (fs.existsSync(scriptPath)) {
      console.log('执行数据库初始化脚本...');
      execSync(`node ${scriptPath}`, { stdio: 'inherit' });
    } else {
      console.warn('数据库初始化脚本不存在:', scriptPath);
      console.log('跳过数据库初始化');
    }
  } catch (error) {
    console.error('数据库初始化失败:', error.message);
    console.log('继续启动应用程序...');
  }
}

// 启动服务器
function startServer() {
  // 检查是否为 standalone 模式
  const standalonePath = path.resolve(process.cwd(), '.next/standalone/server.js');
  const customServerPath = path.resolve(process.cwd(), 'server.js');

  if (fs.existsSync(standalonePath)) {
    console.log('以 standalone 模式启动服务器...');
    process.chdir(path.dirname(standalonePath)); // 切换到 standalone 目录
    require(standalonePath);
  } else if (fs.existsSync(customServerPath)) {
    console.log('使用自定义服务器启动...');
    require(customServerPath);
  } else {
    console.log('使用 npm start 启动服务器...');
    const npm = spawn('npm', ['start'], { stdio: 'inherit' });

    npm.on('close', (code) => {
      if (code !== 0) {
        console.error(`npm start 进程退出，退出码: ${code}`);
        process.exit(code);
      }
    });
  }
}

// 主函数
function main() {
  // 加载环境变量
  loadEnv();

  // 检查并初始化数据库
  checkDatabase();

  // 启动服务器
  startServer();
}

// 执行主函数
main();
