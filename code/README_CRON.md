# Cron 定时任务设置

## 快速设置

### 1. 自动安装（推荐）
```bash
# 进入项目目录
cd /path/to/your/wechat_notifier/code

# 测试脚本
./scripts/setup-cron.sh test

# 安装 cron 任务
./scripts/setup-cron.sh install
```

### 2. 手动设置
```bash
# 编辑 crontab
crontab -e

# 添加以下行（每天凌晨4点执行）
0 4 * * * /usr/bin/node /path/to/project/scripts/send-notifications.js >> /path/to/project/logs/wechat-cron.log 2>&1
```

## 管理命令

```bash
# 查看当前任务
crontab -l

# 查看执行日志
tail -f logs/wechat-cron.log

# 手动测试
node scripts/send-notifications.js

# 卸载任务
./scripts/setup-cron.sh uninstall
```

## 重要配置

1. **服务地址**: 确保服务运行在 `http://127.0.0.1:3000`
2. **密钥配置**: 检查 `.env` 文件中的 `SCHEDULER_SECRET_KEY`
3. **日志位置**: `logs/wechat-cron.log` 和 `logs/wechat-notifications.log`

## 时间配置

修改 crontab 中的时间格式：`分 时 日 月 星期`

常用示例：
- `0 4 * * *` - 每天凌晨4点
- `0 6 * * *` - 每天早上6点  
- `30 3 * * *` - 每天凌晨3点30分
- `0 8 * * 1-5` - 工作日早上8点

## 故障排除

1. **权限问题**: 确保脚本有执行权限 `chmod +x scripts/send-notifications.js`
2. **服务未运行**: 检查 Next.js 服务是否在 3001 端口运行
3. **路径问题**: 在 crontab 中使用完整的文件路径
4. **环境变量**: 确保 `.env` 文件配置正确

## 生产环境建议

- 使用进程管理器（PM2、systemd）保持服务运行
- 设置日志轮转和监控
- 配置邮件或其他方式的失败通知
- 定期备份数据库
