# 微信二维码通知系统

一个基于 Next.js 构建的微信公众号二维码通知管理平台，允许管理员创建和管理微信二维码，用户扫描二维码后，系统可以按照预设的时间向这些用户发送定期通知。

## 功能特点

- 管理员登录认证
- 二维码管理（使用固定的微信模板）
- 订阅者管理
- 通知发送和日志记录
- 定时任务自动发送通知

## 技术栈

- **前端**：Next.js 14.2.3、React、Tailwind CSS
- **后端**：Next.js API Routes
- **数据库**：SQLite（通过 Prisma ORM）
- **认证**：JWT
- **微信集成**：自定义 WeChatAPI 类（基于 Axios）

## 快速开始

### 环境要求

- Node.js 18.0.0 或更高版本
- SQLite3

### 安装

1. 克隆仓库

```bash
git clone <repository-url>
cd wechat-notifier
```

2. 安装依赖

```bash
npm install
```

3. 配置环境变量

复制 `.env.example` 文件为 `.env`，并根据需要修改配置：

```bash
cp .env.example .env
```

主要配置项：
- `DATABASE_URL`：数据库目录路径
- `JWT_SECRET`：JWT 密钥
- `ADMIN_USERNAME`：管理员用户名
- `ADMIN_PASSWORD`：管理员密码
- `WECHAT_APP_ID`：微信公众号 AppID
- `WECHAT_APP_SECRET`：微信公众号 AppSecret
- `WECHAT_TOKEN`：微信公众号 Token
- `SCHEDULER_SECRET_KEY`：定时任务 API 密钥

4. 初始化数据库

```bash
npm run db:init
```

> 注意：在执行 `npm run build` 时会自动检查并初始化数据库，无需手动执行此步骤。
> 本项目使用JSON文件存储数据，DATABASE_URL指向存储JSON文件的目录路径。

5. 启动开发服务器

```bash
npm run dev
```

6. 构建生产版本

```bash
npm run build
```

7. 启动生产服务器

有多种方式可以启动生产服务器：

```bash
# 标准方式启动
npm run start

# 使用自定义服务器启动（包含数据库检查）
npm run start:custom

# 构建后使用以下命令启动 standalone 输出（包含数据库检查）
npm run start:standalone

# 或者直接使用 Node.js 启动 standalone 输出
node start.js
```

> 注意：使用 `node .next/standalone/server.js` 直接启动服务器将跳过数据库初始化检查。建议使用上述命令之一启动服务器。

## 定时任务配置

系统需要设置定时任务来触发通知发送。推荐使用 cron 作业或其他调度工具定期调用 `/api/scheduler` 接口，例如：

```
0 * * * * curl -X POST -H "x-scheduler-key: your-secret-key" https://your-domain.com/api/scheduler
```

## 微信公众号配置

微信公众号需要进行以下配置：
- 设置服务器地址为 `https://your-domain.com/api/wechat/webhook`
- 配置 Token 与系统环境变量一致
- 开启消息加解密（可选）
- 添加模板消息模板

## 许可证

[MIT](LICENSE)
