import type { NextApiRequest, NextApiResponse } from "next";
import { withAuth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { generateTemplateData } from "@/lib/config";

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const id = Number(req.query.id);
  if (isNaN(id)) {
    return res.status(400).json({ error: "无效ID" });
  }

  if (req.method === "GET") {
    // 二维码详情
    const qrcode = await prisma.qRCode.findUnique({
      where: { id },
      include: {
        subscribers: true,
        template: true,
      },
    });
    if (!qrcode) return res.status(404).json({ error: "二维码不存在" });
    return res.status(200).json({ qrcode });
  }

  if (req.method === "PUT") {
    // 编辑二维码/激活/停用
    const {
      name,
      monthlyNotifyDay,
      firstNotifyDate,
      isActive,
      endDate,
    } = req.body;
    try {
      // 获取当前二维码数据
      const currentQRCode = await prisma.qRCode.findUnique({
        where: { id },
        include: {
          template: true,
        },
      });

      if (!currentQRCode) {
        return res.status(404).json({ error: "二维码不存在" });
      }

      // 解析当前模板数据
      const currentTemplateData = JSON.parse(currentQRCode.templateData);

      // 如果有更新还款日期，则需要更新模板数据
      let templateData = undefined;
      if (firstNotifyDate) {
        const newPaymentDate = new Date(firstNotifyDate).toISOString().split('T')[0];

        templateData = JSON.stringify({
          ...currentTemplateData,
          time6: {
            value: newPaymentDate,
          },
        });
      }

      const qrcode = await prisma.qRCode.update({
        where: { id },
        data: {
          name,
          monthlyNotifyDay: monthlyNotifyDay ? Number(monthlyNotifyDay) : undefined,
          firstNotifyDate: firstNotifyDate ? new Date(firstNotifyDate) : undefined,
          templateData,
          isActive: typeof isActive === "boolean" ? isActive : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
        },
      });
      return res.status(200).json({ qrcode });
    } catch (e: any) {
      return res.status(400).json({ error: e.message });
    }
  }

  if (req.method === "DELETE") {
    // 删除二维码
    try {
      await prisma.qRCode.delete({ where: { id } });
      return res.status(204).end();
    } catch (e: any) {
      return res.status(400).json({ error: e.message });
    }
  }

  res.status(405).json({ error: "Method not allowed" });
}

export default withAuth(handler);
