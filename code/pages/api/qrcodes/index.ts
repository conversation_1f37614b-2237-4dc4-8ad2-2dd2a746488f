import type { NextApiRequest, NextApiResponse } from "next";
import { withAuth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { generateTemplateData, WECHAT_TEMPLATE } from "@/lib/config";
import { WeChatAPI } from "@/lib/wechat";

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === "GET") {
    // 二维码列表
    const qrcodes = await prisma.qRCode.findMany({
      include: {
        subscribers: true,
        template: true,
      },
      orderBy: { id: "desc" },
    });
    return res.status(200).json({ qrcodes });
  }

  if (req.method === "POST") {
    // 创建二维码
    const {
      name,
      sceneStr,
      projectName,
      paymentDay,
      amount,
      advanceDays,
      notifyCount,
      firstNotifyDate,
    } = req.body;
    if (!name || !sceneStr || !projectName || !paymentDay || !amount || !firstNotifyDate) {
      return res.status(400).json({ error: "参数不完整" });
    }
    try {
      // 检查场景字符串是否已存在
      const existingQRCode = await prisma.qRCode.findUnique({
        where: { sceneStr },
      });

      if (existingQRCode) {
        return res.status(400).json({ error: "场景字符串已存在，请重新生成" });
      }

      // 获取或创建模板
      let template = await prisma.template.findUnique({
        where: { templateId: WECHAT_TEMPLATE.templateId },
      });

      if (!template) {
        template = await prisma.template.create({
          data: {
            templateId: WECHAT_TEMPLATE.templateId,
            name: WECHAT_TEMPLATE.title,
            description: WECHAT_TEMPLATE.description,
            variables: JSON.stringify(WECHAT_TEMPLATE.content),
            isActive: true,
          },
        });
      }

      // 生成模板数据
      const templateData = JSON.stringify(generateTemplateData(
        projectName,
        new Date(firstNotifyDate).toISOString().split('T')[0],
        amount
      ));

      // 创建二维码记录
      const qrcode = await prisma.qRCode.create({
        data: {
          name,
          sceneStr,
          templateId: template.id,
          templateData,
          firstNotifyDate: new Date(firstNotifyDate),
          monthlyNotifyDay: Number(paymentDay),
          isActive: true,
        },
      });

      try {
        // 生成微信二维码
        const qr = await WeChatAPI.createPermanentQRCode(sceneStr);

        // 更新二维码记录，添加微信二维码信息
        await prisma.qRCode.update({
          where: { id: qrcode.id },
          data: {
            qrTicket: qr.ticket,
            qrUrl: qr.url,
            qrImgUrl: qr.imgUrl,
            qrImgBase64: qr.imgBase64,
            qrGeneratedAt: new Date()
          }
        });

        // 返回带有二维码信息的完整记录
        return res.status(201).json({
          qrcode: {
            ...qrcode,
            qrTicket: qr.ticket,
            qrUrl: qr.url,
            qrImgUrl: qr.imgUrl,
            qrImgBase64: qr.imgBase64,
            qrGeneratedAt: new Date()
          }
        });
      } catch (qrError) {
        console.error("生成二维码失败，但记录已创建:", qrError);
        // 即使二维码生成失败，也返回创建的记录
        return res.status(201).json({
          qrcode,
          warning: "二维码生成失败，请稍后在详情页面重新生成"
        });
      }
    } catch (e: any) {
      return res.status(400).json({ error: e.message });
    }
  }

  res.status(405).json({ error: "Method not allowed" });
}

export default withAuth(handler);
