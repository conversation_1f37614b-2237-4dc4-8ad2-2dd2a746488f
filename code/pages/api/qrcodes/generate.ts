import type { NextApiRequest, NextApiResponse } from "next";
import { withAuth } from "@/lib/auth";
import { WeChatAPI } from "@/lib/wechat";
import { prisma } from "@/lib/prisma";

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { sceneStr, qrcodeId } = req.body;

  if (!sceneStr || !qrcodeId) {
    return res.status(400).json({ error: "sceneStr 和 qrcodeId 不能为空" });
  }

  try {
    // 查找二维码记录
    const qrcode = prisma.qRCode.findUnique({
      where: { id: Number(qrcodeId) }
    });

    if (!qrcode) {
      return res.status(404).json({ error: "二维码记录不存在" });
    }

    // 检查是否需要重新生成二维码
    let needRegenerate = true;

    if (qrcode.qrGeneratedAt && qrcode.qrTicket) {
      const now = new Date();
      const generatedAt = new Date(qrcode.qrGeneratedAt);
      const daysDiff = Math.floor((now.getTime() - generatedAt.getTime()) / (1000 * 60 * 60 * 24));

      // 如果二维码生成时间在29天内，且已有票据，则不需要重新生成
      if (daysDiff < 29) {
        needRegenerate = false;
      }
    }

    let qr;

    if (needRegenerate) {
      // 生成新的二维码
      qr = await WeChatAPI.createPermanentQRCode(sceneStr);

      // 更新数据库中的二维码信息
      await prisma.qRCode.update({
        where: { id: Number(qrcodeId) },
        data: {
          qrTicket: qr.ticket,
          qrUrl: qr.url,
          qrImgUrl: qr.imgUrl,
          qrImgBase64: qr.imgBase64,
          qrGeneratedAt: new Date()
        }
      });
    } else {
      // 使用已有的二维码信息
      qr = {
        ticket: qrcode.qrTicket,
        url: qrcode.qrUrl,
        imgUrl: qrcode.qrImgUrl,
        imgBase64: qrcode.qrImgBase64
      };
    }

    return res.status(200).json({ qr, regenerated: needRegenerate });
  } catch (e: any) {
    return res.status(400).json({ error: e.message });
  }
}

export default withAuth(handler);
