import type { NextApiRequest, NextApiResponse } from "next";
import { withAuth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { WeChatAPI } from "@/lib/wechat";
import { WECHAT_TEMPLATE, getNextPaymentDate, formatChineseDate, generateTemplateData } from "@/lib/config";

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const id = Number(req.query.id);
  if (isNaN(id)) {
    return res.status(400).json({ error: "无效ID" });
  }

  try {
    // 获取订阅者信息，包括关联的二维码
    const subscriber = await prisma.subscriber.findUnique({
      where: { id },
      include: {
        qrcode: {
          include: { template: true }
        }
      },
    });

    if (!subscriber) {
      return res.status(404).json({ error: "订阅者不存在" });
    }

    if (!subscriber.isActive) {
      return res.status(400).json({ error: "订阅者未激活，无法发送消息" });
    }

    // 解析二维码的模板数据以获取项目信息
    const templateData = JSON.parse(subscriber.qrcode.templateData);
    const projectName = templateData.thing12?.value || "项目";
    const amount = templateData.amount3?.value || "0";

    // 计算下一期还款日期
    const nextPaymentDate = getNextPaymentDate(subscriber.qrcode.monthlyNotifyDay);
    const formattedPaymentDate = formatChineseDate(nextPaymentDate);

    // 生成测试消息的模板数据
    const testTemplateData = generateTemplateData(
      `【测试】${projectName}`,
      formattedPaymentDate,
      amount
    );

    // 发送模板消息
    await WeChatAPI.sendTemplateMessage(
      subscriber.openid,
      WECHAT_TEMPLATE.templateId,
      testTemplateData
    );

    // 记录通知日志
    await prisma.notificationLog.create({
      data: {
        subscriberId: subscriber.id,
        status: "success",
      },
    });

    res.status(200).json({ 
      success: true, 
      message: "测试消息发送成功",
      data: {
        projectName: `【测试】${projectName}`,
        paymentDate: formattedPaymentDate,
        amount: amount
      }
    });

  } catch (error: any) {
    console.error("发送测试消息失败:", error);
    
    // 尝试记录失败日志（如果订阅者存在的话）
    try {
      await prisma.notificationLog.create({
        data: {
          subscriberId: id,
          status: "fail",
          error: error.message,
        },
      });
    } catch (logError) {
      console.error("记录失败日志时出错:", logError);
    }

    res.status(500).json({ 
      error: "发送测试消息失败", 
      details: error.message 
    });
  }
}

export default withAuth(handler);
