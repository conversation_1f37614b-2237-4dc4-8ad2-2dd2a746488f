import type { NextApiRequest, NextApiResponse } from "next";
import { withAuth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === "GET") {
    // 订阅者列表
    const subscribers = await prisma.subscriber.findMany({
      include: {
        qrcode: true,
        notifications: true,
      },
      orderBy: { id: "desc" },
    });
    return res.status(200).json({ subscribers });
  }

  res.status(405).json({ error: "Method not allowed" });
}

export default withAuth(handler);
