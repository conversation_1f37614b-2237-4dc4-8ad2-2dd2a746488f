import type { NextApiRequest, NextApiResponse } from "next";
import { withAuth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const id = Number(req.query.id);
  if (isNaN(id)) {
    return res.status(400).json({ error: "无效ID" });
  }

  if (req.method === "GET") {
    // 订阅者详情
    const subscriber = await prisma.subscriber.findUnique({
      where: { id },
      include: {
        qrcode: true,
        notifications: {
          orderBy: { sentAt: 'desc' },
        },
        scheduledNotifications: {
          orderBy: { scheduledDate: 'asc' },
        },
      },
    });
    if (!subscriber) return res.status(404).json({ error: "订阅者不存在" });
    return res.status(200).json({ subscriber });
  }

  if (req.method === "PUT") {
    // 激活/停用订阅者
    const { isActive } = req.body;
    try {
      const subscriber = await prisma.subscriber.update({
        where: { id },
        data: {
          isActive: typeof isActive === "boolean" ? isActive : undefined,
        },
      });
      return res.status(200).json({ subscriber });
    } catch (e: any) {
      return res.status(400).json({ error: e.message });
    }
  }

  if (req.method === "DELETE") {
    // 删除订阅者
    try {
      await prisma.subscriber.delete({ where: { id } });
      return res.status(204).end();
    } catch (e: any) {
      return res.status(400).json({ error: e.message });
    }
  }

  res.status(405).json({ error: "Method not allowed" });
}

export default withAuth(handler);
