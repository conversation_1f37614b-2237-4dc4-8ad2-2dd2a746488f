import type { NextApiRequest, NextApiResponse } from "next";
import { signJwt } from "@/lib/jwt";
import bcrypt from "bcryptjs";

const ADMIN_USERNAME = process.env.ADMIN_USERNAME!;
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD!;

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { username, password } = req.body;

  if (!username || !password) {
    return res.status(400).json({ error: "用户名和密码不能为空" });
  }

  // 只允许环境变量中的管理员账号
  if (username !== ADMIN_USERNAME) {
    return res.status(401).json({ error: "用户名或密码错误" });
  }

  // 支持明文或 bcrypt 加密存储
  let valid = false;
  if (ADMIN_PASSWORD.startsWith("$2a$") || ADMIN_PASSWORD.startsWith("$2b$")) {
    valid = await bcrypt.compare(password, ADMIN_PASSWORD);
  } else {
    valid = password === ADMIN_PASSWORD;
  }

  if (!valid) {
    return res.status(401).json({ error: "用户名或密码错误" });
  }

  const token = signJwt({ username });

  res.status(200).json({ token });
}
