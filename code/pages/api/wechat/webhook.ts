import type { NextApiRequest, NextApiResponse } from "next";
import { WeChatAPI } from "@/lib/wechat";
import { prisma } from "@/lib/prisma";
import { WECHAT_TEMPLATE, getNextPaymentDate, formatChineseDate, generateTemplateData, generateNotificationSchedule } from "@/lib/config";
import crypto from "crypto";

// 微信服务器验证
function checkSignature(token: string, timestamp: string, nonce: string, signature: string) {
  const arr = [token, timestamp, nonce].sort();
  const str = arr.join("");
  const sha1 = crypto.createHash("sha1").update(str).digest("hex");
  return sha1 === signature;
}

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { signature, timestamp, nonce, echostr } = req.query as Record<string, string>;
  const token = process.env.WECHAT_TOKEN!;

  if (req.method === "GET") {
    // 微信服务器验证
    if (checkSignature(token, timestamp, nonce, signature)) {
      res.status(200).send(echostr);
    } else {
      res.status(401).send("Invalid signature");
    }
    return;
  }

  if (req.method === "POST") {
    // 读取原始 XML
    let xml = "";
    await new Promise<void>((resolve) => {
      req.on("data", (chunk) => {
        xml += chunk;
      });
      req.on("end", () => resolve());
    });

    const data = await WeChatAPI.parseXML(xml);
    const msg = data.xml;

    // 处理事件
    if (msg.MsgType === "event") {
      if (msg.Event === "subscribe" || msg.Event === "SCAN") {
        // 用户扫码关注或已关注扫码
        const openid = msg.FromUserName;
        const sceneStr = msg.EventKey?.replace(/^qrscene_/, "");
        if (sceneStr) {
          // 查找二维码
          const qrcode = await prisma.qRCode.findUnique({
            where: { sceneStr },
            include: { template: true }
          });
          if (qrcode) {
            // 订阅者入库或激活
            const subscriber = await prisma.subscriber.upsert({
              where: {
                openid_qrcodeId: {
                  openid: openid,
                  qrcodeId: qrcode.id
                }
              },
              update: { isActive: true },
              create: { openid, qrcodeId: qrcode.id },
            });

            // 检查是否已存在通知计划，如果不存在则生成
            const existingSchedule = await prisma.scheduledNotification.findFirst({
              where: { subscriberId: subscriber.id }
            });

            if (!existingSchedule) {
              // 生成通知计划
              const scheduleDate = generateNotificationSchedule(
                qrcode.firstNotifyDate,
                qrcode.monthlyNotifyDay,
                qrcode.endDate
              );

              // 批量创建通知计划
              if (scheduleDate.length > 0) {
                for (const date of scheduleDate) {
                  try {
                    await prisma.scheduledNotification.create({
                      data: {
                        subscriberId: subscriber.id,
                        scheduledDate: date,
                      },
                    });
                  } catch (error) {
                    // 忽略重复键错误，继续处理下一个
                    if (!error.message.includes('Unique constraint')) {
                      throw error;
                    }
                  }
                }
              }
            }

            // 发送下一期还款事件的模板消息
            try {
              // 解析二维码的模板数据以获取项目信息
              const templateData = JSON.parse(qrcode.templateData);
              const projectName = templateData.thing12?.value || "项目";
              const amount = templateData.amount3?.value || "0";

              // 计算下一期还款日期
              const nextPaymentDate = getNextPaymentDate(qrcode.monthlyNotifyDay);
              const formattedPaymentDate = formatChineseDate(nextPaymentDate);

              // 生成欢迎消息的模板数据
              const welcomeTemplateData = generateTemplateData(
                projectName,
                formattedPaymentDate,
                amount
              );

              // 发送模板消息
              await WeChatAPI.sendTemplateMessage(
                openid,
                WECHAT_TEMPLATE.templateId,
                welcomeTemplateData
              );

              // 记录通知日志
              await prisma.notificationLog.create({
                data: {
                  subscriberId: subscriber.id,
                  status: "success",
                },
              });
            } catch (error: any) {
              console.error("发送欢迎消息失败:", error);
              // 记录失败日志
              await prisma.notificationLog.create({
                data: {
                  subscriberId: subscriber.id,
                  status: "fail",
                  error: error.message,
                },
              });
            }
          }
        }
      }
      if (msg.Event === "unsubscribe") {
        // 取消关注，设置订阅者为不活跃
        const openid = msg.FromUserName;
        await prisma.subscriber.updateMany({
          where: { openid },
          data: { isActive: false },
        });
      }
    }

    res.status(200).send("success");
    return;
  }

  res.status(405).send("Method not allowed");
}
