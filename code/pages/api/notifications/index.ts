import type { NextApiRequest, NextApiResponse } from "next";
import { withAuth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === "GET") {
    // 通知日志列表
    const logs = await prisma.notificationLog.findMany({
      include: {
        subscriber: true,
      },
      orderBy: { sentAt: "desc" },
      take: 200,
    });

    // 统计信息
    const total = await prisma.notificationLog.count();
    const success = await prisma.notificationLog.count({ where: { status: "success" } });
    const fail = total - success;
    const successRate = total > 0 ? Math.round((success / total) * 100) : 0;

    return res.status(200).json({
      logs,
      stats: {
        total,
        success,
        fail,
        successRate,
      },
    });
  }

  res.status(405).json({ error: "Method not allowed" });
}

export default withAuth(handler);
