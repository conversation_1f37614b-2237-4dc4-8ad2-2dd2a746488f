# 数据库迁移指南

本项目已从JSON文件数据库迁移到使用Prisma + SQLite。

## 迁移状态

✅ **迁移准备完成！**

根据验证结果：
- ✓ DATABASE_URL配置正确: `file:./data.db`
- ✓ Prisma schema包含5个数据模型
- ✓ Prisma客户端配置正确
- ✓ 发现3条记录待迁移（1个用户，1个模板，1个二维码）
- ✓ 所有迁移脚本就绪

## 迁移步骤

### 1. 安装依赖
```bash
cd code
npm install
```

### 2. 初始化数据库
```bash
npm run db:init
```
这将：
- 生成Prisma客户端
- 创建SQLite数据库文件
- 推送数据库schema

### 3. 从JSON迁移数据
```bash
npm run db:migrate
```
这将把`db/`目录下的JSON文件数据迁移到SQLite数据库中。

### 4. 验证迁移
```bash
npm run db:studio
```
打开Prisma Studio查看数据库内容。

## 数据库配置

数据库配置在`.env`文件中：
```
DATABASE_URL="file:./data.db"
```

## 主要变更

1. **数据库层**：
   - 从JSON文件存储改为SQLite数据库
   - 使用Prisma ORM进行数据库操作
   - 支持关系查询和事务

2. **API层**：
   - 所有API路由现在使用真正的Prisma客户端
   - 数据库操作现在是异步的（使用await）
   - 更好的类型安全性

3. **数据模型**：
   - 用户表：存储管理员用户信息
   - 模板表：微信消息模板
   - 二维码表：二维码配置和状态
   - 订阅者表：扫码用户信息
   - 通知日志表：消息发送记录

## 开发命令

- `npm run dev` - 启动开发服务器
- `npm run db:generate` - 生成Prisma客户端
- `npm run db:push` - 推送schema到数据库
- `npm run db:studio` - 打开Prisma Studio
- `npm run db:migrate` - 从JSON迁移数据

## 注意事项

1. 确保`.env`文件中的`DATABASE_URL`正确配置
2. 首次运行时会自动初始化数据库
3. 旧的JSON文件仍然保留，可以作为备份
4. 迁移脚本是幂等的，可以安全地多次运行
